2025-06-18 11:04:07,204 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 11:04:07,229 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 11:05:07,576 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 11:05:07,598 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 11:05:07,610 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 11:05:07,638 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 11:05:07,642 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 11:05:07,675 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 11:05:07,693 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 11:05:07,723 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 11:05:07,729 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 11:06:09,504 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 11:06:09,556 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 11:06:09,594 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 11:06:09,644 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 11:06:09,715 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 11:06:09,724 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 11:06:09,739 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 11:06:09,746 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 11:06:09,804 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 11:07:10,101 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 11:07:10,106 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 11:07:10,156 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 11:07:10,228 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 11:07:10,235 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 11:07:10,240 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 11:07:10,303 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 11:07:10,363 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 11:16:22,422 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 11:16:22,429 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 11:16:22,461 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 11:16:22,495 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 11:16:22,584 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 11:16:22,603 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 11:16:22,615 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 11:16:22,752 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 11:31:40,570 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 11:31:40,663 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 11:31:40,676 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-18 11:31:40,688 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 11:31:40,721 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 11:31:40,770 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 11:31:40,796 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 11:31:40,809 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-18 11:31:40,825 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 11:31:40,856 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 11:31:40,884 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 12:01:17,762 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 12:01:17,774 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 12:01:17,786 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:01:17,794 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-18 12:01:17,798 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:01:17,802 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:01:17,805 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 12:01:17,824 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-18 12:01:17,832 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 12:01:17,838 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 12:01:17,844 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:01:17,865 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:01:17,875 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:01:17,895 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-18 12:01:17,904 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:01:17,913 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:01:17,926 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 12:01:17,929 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-18 12:01:17,936 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 12:01:17,950 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:01:17,955 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:01:18,005 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-18 12:01:18,017 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 12:01:18,039 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:01:18,044 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-18 12:01:18,047 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-18 12:02:21,925 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:02:21,984 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:02:22,004 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:02:22,044 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 12:02:22,062 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:02:22,110 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:02:22,116 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:02:22,164 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:02:22,177 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-18 12:02:22,229 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:02:22,238 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:02:22,246 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 12:02:22,287 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:02:22,325 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:02:22,349 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 12:03:23,443 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:03:23,460 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:03:23,488 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:03:23,537 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:03:23,544 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:03:23,651 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:03:23,721 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:03:23,738 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:03:23,782 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 12:03:23,811 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:03:23,851 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:03:23,863 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 12:03:23,888 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:04:24,591 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:04:24,606 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:04:24,647 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:04:24,654 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 12:04:24,685 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:04:24,693 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:04:24,708 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:04:24,719 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:04:24,788 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:04:24,797 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 12:04:24,815 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:04:24,892 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:04:24,943 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:05:26,081 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:05:26,093 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 12:05:26,113 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:05:26,123 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 12:05:26,128 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:05:26,138 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:05:26,199 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 12:05:26,230 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:05:26,242 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:05:26,248 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:05:26,313 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 12:05:26,324 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 12:05:26,334 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:05:26,346 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 12:05:26,360 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:05:26,371 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 12:05:26,376 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 12:05:26,383 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:05:26,423 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:06:27,068 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:06:27,088 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:06:27,104 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:06:27,130 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:06:27,169 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:06:27,173 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:06:27,205 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:06:27,223 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:06:27,247 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:06:27,259 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:06:27,333 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:07:30,060 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:07:30,084 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:07:30,142 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:07:30,162 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:07:30,190 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:07:30,202 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:07:30,305 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:07:30,324 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:07:30,384 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:07:30,390 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:07:30,594 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:08:30,677 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:08:30,719 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:08:30,755 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:08:30,790 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:08:30,811 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:08:30,986 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:08:30,995 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:08:30,999 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:08:31,028 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:08:31,043 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:08:31,084 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:09:32,800 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:09:32,817 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 12:09:32,821 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:09:32,865 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:09:32,888 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 12:09:32,918 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 12:09:32,931 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:09:32,980 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:09:32,986 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:09:32,990 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:09:33,006 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:09:33,024 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 12:09:33,034 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:09:33,093 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 12:09:33,180 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:09:33,200 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 12:09:33,254 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:10:35,143 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 12:10:35,167 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 12:10:35,183 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:10:35,189 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:10:35,192 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:10:35,205 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 12:10:35,230 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:10:35,279 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:10:35,306 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 12:10:35,342 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:10:35,382 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 12:10:35,402 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:10:35,406 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 12:10:35,413 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:10:35,429 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:10:35,457 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:10:35,478 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:11:35,517 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 12:11:35,561 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 12:11:35,575 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 12:11:35,582 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 12:11:35,607 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 12:11:35,633 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:11:35,666 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 12:11:35,672 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 12:11:35,706 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 12:11:35,713 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 12:11:35,721 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 12:11:35,747 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:11:35,780 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 12:11:35,794 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:11:35,798 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 12:11:35,814 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:11:35,843 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 12:12:38,710 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 12:12:38,755 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 12:12:38,760 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 12:12:38,930 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 12:16:44,185 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 12:16:44,317 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 12:16:44,341 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 12:16:44,407 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 12:16:44,426 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 12:16:44,442 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 12:16:44,481 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 12:16:44,515 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 12:16:44,539 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 12:31:13,498 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for health
2025-06-18 12:31:13,576 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 12:31:13,657 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 12:31:13,861 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 12:31:13,887 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 12:46:31,382 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 12:46:31,466 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 12:46:31,497 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 13:01:49,067 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-18 13:01:49,086 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 13:01:49,090 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 13:01:49,093 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 13:01:49,098 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-18 13:01:49,108 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 13:01:49,114 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 13:01:49,117 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 13:01:49,140 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 13:01:49,144 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-18 13:01:49,152 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 13:01:49,155 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 13:01:49,158 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-18 13:01:49,161 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 13:01:49,168 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 13:01:49,180 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-18 13:01:49,186 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 13:01:49,191 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 13:01:49,196 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-18 13:01:49,200 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-18 13:01:49,223 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 13:01:49,226 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 13:01:49,229 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-18 13:01:49,251 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-18 13:01:49,255 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 13:01:49,261 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 13:01:49,265 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 13:01:49,269 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-18 13:01:49,275 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-18 13:01:49,287 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 13:01:49,290 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-18 13:01:49,295 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 13:01:49,315 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 13:01:49,326 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 13:01:49,336 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 13:01:49,342 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 13:01:49,353 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 13:01:49,357 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 13:01:49,361 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 13:02:50,182 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 13:02:50,192 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 13:02:50,198 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 13:02:50,201 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 13:02:50,204 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 13:02:50,210 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 13:02:50,215 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-18 13:02:50,221 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 13:02:50,240 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 13:02:50,245 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 13:02:50,262 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 13:02:50,277 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 13:02:50,287 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 13:02:50,300 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-18 13:02:50,315 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 13:02:50,320 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 13:02:50,324 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 13:02:50,336 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 13:02:50,344 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 13:02:50,352 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-18 13:02:50,363 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-18 13:02:50,374 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 13:02:50,380 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 13:02:50,385 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 13:02:50,389 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 13:02:50,394 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-18 13:02:50,398 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-18 13:02:50,402 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 13:02:50,406 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 13:02:50,411 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-18 13:02:50,415 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-18 13:02:50,419 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-18 13:02:50,429 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-18 13:02:50,434 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 13:02:50,437 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-18 13:02:50,446 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 13:02:50,455 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 13:02:50,475 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 13:02:50,488 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-18 13:03:51,380 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-18 13:03:51,386 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-18 13:03:51,394 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 13:03:51,401 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-18 13:03:51,404 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-18 13:03:51,407 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 13:03:51,418 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 13:03:51,423 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 13:03:51,432 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 13:03:51,438 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 13:03:51,451 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 13:03:51,462 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 13:03:51,468 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 13:03:51,478 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-18 13:03:51,481 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-18 13:03:51,495 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 13:03:51,515 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 13:03:51,536 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 13:03:51,540 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 13:03:51,559 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 13:03:51,563 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 13:03:51,567 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 13:03:51,570 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 13:03:51,580 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 13:03:51,584 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 13:03:51,594 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-18 13:03:51,609 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 13:03:51,616 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-18 13:03:51,622 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 13:03:51,631 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 13:03:51,637 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-18 13:03:51,641 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-18 13:03:51,647 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-18 13:03:51,651 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 13:03:51,679 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 13:03:51,682 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 13:03:51,693 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 13:03:51,714 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-18 13:03:51,718 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 13:04:53,529 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 13:04:53,560 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-18 13:04:53,568 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 13:04:53,593 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 13:04:53,610 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 13:04:53,619 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 13:04:53,647 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 13:04:53,729 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 13:04:53,741 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for health
2025-06-18 13:04:53,749 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 13:04:53,770 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-18 13:04:53,775 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 13:04:53,781 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 13:04:53,786 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-18 13:04:53,819 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 13:04:53,834 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 13:04:53,842 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 13:04:53,846 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-18 13:04:53,851 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 13:04:53,857 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 13:04:53,861 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-18 13:04:53,865 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 13:04:53,878 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 13:04:53,882 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 13:04:53,887 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 13:04:53,899 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 13:04:53,905 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 13:04:53,913 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for health
2025-06-18 13:04:53,923 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-18 13:04:53,942 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 13:04:53,948 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-18 13:04:53,963 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 13:04:53,982 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-18 13:04:54,010 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 13:04:54,018 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-18 13:04:54,023 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 13:04:54,041 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-18 13:04:54,101 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 13:04:54,124 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 13:05:54,644 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 13:05:54,648 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 13:05:54,651 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 13:05:54,666 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 13:05:54,672 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 13:05:54,687 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 13:05:54,697 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 13:05:54,707 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 13:05:54,713 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 13:05:54,724 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 13:05:54,728 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 13:05:54,734 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.set_uninvoiced_so_closed because it was found in queue for health
2025-06-18 13:05:54,740 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 13:05:54,763 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 13:05:54,768 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 13:05:54,771 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 13:05:54,797 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 13:05:54,803 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 13:05:54,806 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 13:05:54,830 ERROR scheduler Skipped queueing my_test_hourly because it was found in queue for health
2025-06-18 13:05:54,838 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 13:05:54,847 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 13:05:54,866 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-18 13:05:54,871 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-18 13:05:54,874 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 13:05:54,880 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 13:05:54,886 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
2025-06-18 13:05:54,900 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 13:05:54,904 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-18 13:06:56,244 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 13:06:56,314 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 13:06:56,319 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 13:06:56,366 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 13:06:56,369 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 13:06:56,430 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 13:16:40,947 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 13:16:41,061 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 13:31:59,557 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 13:31:59,587 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 13:31:59,606 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 13:31:59,625 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-18 13:31:59,663 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 13:46:17,930 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 14:01:35,614 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 14:01:35,621 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 14:01:35,653 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 14:01:35,664 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 14:01:35,675 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 14:01:35,681 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 14:01:35,737 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 14:01:35,751 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for health
2025-06-18 14:01:35,786 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 14:01:35,790 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 14:01:35,794 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 14:01:35,801 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 14:01:35,829 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 14:01:35,849 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 14:01:35,871 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-18 14:01:35,878 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for health
2025-06-18 14:01:35,921 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 14:01:35,934 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 14:01:35,949 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 14:02:36,636 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 14:02:36,679 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 14:02:36,750 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 14:02:36,755 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 14:02:36,766 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 14:02:36,791 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 14:02:36,815 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 14:02:36,827 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 14:02:36,831 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 14:02:36,863 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 14:02:36,874 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 14:02:36,879 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 14:02:36,891 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 14:03:37,929 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 14:03:37,956 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 14:03:37,961 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 14:03:37,992 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 14:03:38,030 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 14:03:38,063 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 14:03:38,090 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 14:03:38,102 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 14:03:38,105 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 14:03:38,121 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 14:03:38,127 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 14:03:38,176 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 14:03:38,202 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 14:04:39,076 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 14:05:40,780 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 14:05:40,809 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 14:05:40,884 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 14:05:40,888 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 14:05:40,928 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 14:05:40,943 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 14:05:40,993 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 14:06:41,076 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 14:06:41,097 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 14:06:41,167 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 14:06:41,216 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 14:06:41,273 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 14:06:41,297 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 14:06:41,327 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 14:07:42,773 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 14:07:42,804 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 14:07:42,832 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 14:07:42,843 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 14:07:42,857 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 14:07:42,883 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 14:07:42,894 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 14:08:43,650 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 14:08:43,683 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 14:08:43,708 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 14:08:43,746 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 14:08:43,755 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 14:08:43,798 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 14:08:43,849 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 14:31:10,099 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 14:31:10,135 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-18 14:31:10,183 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 14:31:10,193 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 14:31:10,206 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 14:46:28,088 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 14:46:28,221 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 15:01:48,047 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 15:01:48,084 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 15:01:48,133 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for health
2025-06-18 15:01:48,148 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 15:01:48,182 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 15:01:48,193 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 15:01:48,204 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 15:01:48,217 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 15:01:48,240 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 15:01:48,253 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 15:01:48,286 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 15:01:48,292 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 15:01:48,304 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 15:01:48,317 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 15:02:48,400 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 15:02:48,431 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 15:02:48,494 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 15:02:48,516 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 15:02:48,535 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 15:02:48,553 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 15:02:48,596 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 15:03:50,048 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 15:03:50,055 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 15:03:50,137 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 15:03:50,172 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 15:03:50,194 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 15:03:50,229 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 15:03:50,254 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 15:04:50,856 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 15:04:50,908 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 15:04:50,913 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 15:04:51,065 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 15:04:51,071 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 15:04:51,112 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 15:04:51,124 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 15:05:52,200 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 15:05:52,215 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 15:05:52,223 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 15:05:52,240 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 15:05:52,264 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 15:05:52,272 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 15:05:52,299 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 15:05:52,327 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 15:05:52,349 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 15:05:52,387 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 15:05:52,396 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 15:05:52,422 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 15:05:52,429 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 15:06:53,789 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 15:06:53,821 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 15:06:53,894 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 15:06:53,897 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for health
2025-06-18 15:06:53,906 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 15:06:53,919 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 15:06:53,946 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 15:06:53,977 ERROR scheduler Skipped queueing healthcare.healthcare.doctype.patient_appointment.patient_appointment.send_appointment_reminder because it was found in queue for health
2025-06-18 15:06:54,000 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 15:06:54,019 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 15:06:54,024 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for health
2025-06-18 15:06:54,033 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 15:06:54,053 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 15:46:40,134 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 15:46:40,176 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 15:46:40,209 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 15:46:40,269 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 15:46:40,337 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 15:46:40,393 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 15:46:40,405 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for health
2025-06-18 15:46:40,415 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 16:01:57,654 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for health
2025-06-18 16:01:57,661 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for health
2025-06-18 16:01:57,681 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for health
2025-06-18 16:01:57,688 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for health
2025-06-18 16:01:57,699 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for health
2025-06-18 16:01:57,708 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for health
2025-06-18 16:01:57,720 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for health
2025-06-18 16:01:57,729 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for health
2025-06-18 16:01:57,757 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for health
2025-06-18 16:01:57,777 ERROR scheduler Skipped queueing hms_tz.nhif.api.healthcare_utils.create_invoiced_items_if_not_created because it was found in queue for health
2025-06-18 16:01:57,782 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for health
2025-06-18 16:01:57,786 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for health
2025-06-18 16:01:57,805 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for health
2025-06-18 16:01:57,831 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for health
2025-06-18 16:01:57,837 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for health
2025-06-18 16:01:57,853 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for health
2025-06-18 16:01:57,857 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for health
2025-06-18 16:01:57,865 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for health
2025-06-18 16:01:57,870 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for health
2025-06-18 16:01:57,878 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for health
2025-06-18 16:01:57,886 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for health
2025-06-18 16:01:57,892 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for health
2025-06-18 16:01:57,907 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for health
2025-06-18 16:01:57,913 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for health
2025-06-18 16:01:57,918 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for health
2025-06-18 16:01:57,923 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for health
2025-06-18 16:01:57,942 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for health
2025-06-18 16:01:57,957 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for health
2025-06-18 16:01:57,969 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for health
2025-06-18 16:01:57,982 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for health
