2025-06-10 11:48:24,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-10 11:48:24,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-10 11:48:24,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-10 11:48:24,761 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 11:48:24,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-10 11:48:27,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0
2025-06-10 12:34:43,663 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-10 12:34:44,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-10 12:34:44,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-10 12:34:45,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-10 12:34:45,299 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-10 12:34:45,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 12:34:45,709 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `ref_vital_signs` varchar(140), MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-10 12:34:46,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-10 12:34:46,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-10 12:34:46,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 12:34:46,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-10 12:34:47,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0
2025-06-10 12:34:47,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-10 12:34:47,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-10 12:34:47,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-10 12:34:51,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `price_list` varchar(140), MODIFY `patient` varchar(140)
2025-06-10 12:34:51,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-10 12:34:52,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-10 12:34:52,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-10 12:34:52,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-10 12:34:52,544 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `f_max_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-10 12:34:52,565 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_code`, DROP INDEX `lab_test_name_index`
2025-06-10 12:34:52,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 12:34:52,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-10 12:34:53,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-10 12:34:53,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-10 12:34:53,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 12:34:54,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `ref_vital_signs` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `appointment_type` varchar(140)
2025-06-10 12:34:54,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-10 12:34:54,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 12:34:55,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 12:34:55,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-10 12:34:55,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9)
2025-06-10 12:34:55,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-10 12:34:55,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-10 12:34:55,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-10 12:35:00,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `price_list` varchar(140), MODIFY `patient` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-10 12:35:00,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 12:35:00,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-10 12:35:00,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-10 12:35:00,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-10 12:35:00,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-10 12:35:00,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-10 12:35:00,874 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-10 12:35:00,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-10 12:35:01,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-10 12:35:01,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-10 12:35:04,285 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-06-10 12:35:04,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `ref_vital_signs` varchar(140), MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-11 16:43:50,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-11 16:44:05,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-12 09:28:54,869 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-12 09:28:55,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-12 09:28:55,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-12 09:28:56,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-12 09:28:56,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-12 09:28:56,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 09:28:56,847 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `ref_vital_signs` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-12 09:28:57,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-12 09:28:57,376 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-12 09:28:57,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 09:28:57,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-12 09:28:58,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-12 09:28:58,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-12 09:28:58,380 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-12 09:28:58,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 09:29:02,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `price_list` varchar(140), MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `patient` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-12 09:29:02,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-12 09:29:02,962 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 09:29:03,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-12 09:29:03,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-12 09:29:03,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-12 09:29:03,417 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_code`, DROP INDEX `lab_test_name_index`
2025-06-12 09:29:03,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 09:29:03,797 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-12 09:29:04,418 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-12 09:29:04,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 09:29:04,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabLease Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `witholding_tax` decimal(21,9) not null default 0
2025-06-12 09:29:04,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 09:29:05,206 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `ref_vital_signs` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `appointment_type` varchar(140)
2025-06-12 09:29:05,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-12 09:29:05,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-12 09:29:06,060 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 09:29:06,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-12 09:29:06,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-12 09:29:06,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-12 09:29:06,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 09:29:06,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-12 09:29:11,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `price_list` varchar(140), MODIFY `patient` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-12 09:29:11,259 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 09:29:11,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-12 09:29:11,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 09:29:11,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-12 09:29:11,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-12 09:29:11,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-12 09:29:11,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-12 09:29:11,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-12 09:29:12,136 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 09:29:12,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-12 09:29:15,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-12 09:29:15,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `ref_vital_signs` varchar(140)
2025-06-12 10:22:03,913 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-12 10:22:04,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-12 10:22:05,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-12 10:22:05,754 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-12 10:22:05,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 10:22:06,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 10:22:06,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `ref_vital_signs` varchar(140)
2025-06-12 10:22:06,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-12 10:22:06,949 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-12 10:22:07,308 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 10:22:07,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-12 10:22:07,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-12 10:22:07,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 10:22:07,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-12 10:22:08,138 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 10:22:12,369 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `patient` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `price_list` varchar(140)
2025-06-12 10:22:12,513 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-12 10:22:12,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 10:22:12,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-12 10:22:12,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-12 10:22:13,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-12 10:22:13,069 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name_index`, DROP INDEX `lab_test_code`
2025-06-12 10:22:13,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 10:22:13,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-12 10:22:14,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-12 10:22:14,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-12 10:22:14,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 10:22:14,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `ref_vital_signs` varchar(140), MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-12 10:22:15,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-12 10:22:15,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 10:22:15,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 10:22:15,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-12 10:22:16,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 10:22:16,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-12 10:22:16,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-12 10:22:16,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-12 10:22:20,701 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `price_list` varchar(140), MODIFY `patient` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `encounter_mode_of_payment` varchar(140)
2025-06-12 10:22:20,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 10:22:20,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-12 10:22:21,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 10:22:21,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-12 10:22:21,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-12 10:22:21,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-12 10:22:21,485 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-12 10:22:21,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-12 10:22:21,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 10:22:21,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-12 10:22:24,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-15 21:18:16,283 WARNING database DDL Query made to DB:
create table `tabMedical Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-15 21:24:22,055 WARNING database DDL Query made to DB:
create table `tabMedical Code Standard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-15 21:27:08,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Code Standard` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `uri` varchar(140)
2025-06-15 21:36:37,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Code` ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `code` varchar(140) unique, ADD COLUMN `uri` varchar(140), ADD COLUMN `description` text
2025-06-15 21:36:37,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Code` ADD UNIQUE INDEX IF NOT EXISTS code (`code`)
2025-06-15 21:43:24,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Code Standard` ADD UNIQUE INDEX IF NOT EXISTS medical_code (`medical_code`)
2025-06-15 22:04:59,792 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-15 22:05:01,710 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-15 22:05:04,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-15 22:05:06,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-15 22:05:07,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-15 22:05:08,074 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-15 22:05:08,727 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `ref_vital_signs` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-15 22:05:10,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-15 22:05:10,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-15 22:05:11,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-15 22:05:12,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-15 22:05:13,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0
2025-06-15 22:05:13,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-15 22:05:13,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-15 22:05:14,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-15 22:05:20,417 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `price_list` varchar(140), MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `patient` varchar(140)
2025-06-15 22:05:20,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-15 22:05:21,197 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-15 22:05:21,473 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-15 22:05:21,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-15 22:05:22,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-15 22:05:22,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name_index`, DROP INDEX `lab_test_code`
2025-06-15 22:05:22,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-15 22:05:23,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-15 22:05:26,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-15 22:05:26,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-15 22:05:27,545 WARNING database DDL Query made to DB:
create table `tabMy website` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`heading` varchar(140),
`description` text,
`route` varchar(140),
`is_published` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-15 22:05:28,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-15 22:05:28,828 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `ref_vital_signs` varchar(140), MODIFY `appointment_type` varchar(140)
2025-06-15 22:05:30,033 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-15 22:05:30,493 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-15 22:05:31,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-15 22:05:31,996 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-15 22:05:32,828 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-15 22:05:33,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-15 22:05:33,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-15 22:05:33,880 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-15 22:05:39,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `patient` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `price_list` varchar(140), MODIFY `encounter_mode_of_payment` varchar(140)
2025-06-15 22:05:40,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0
2025-06-15 22:05:40,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-15 22:05:41,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-15 22:05:41,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-15 22:05:41,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-15 22:05:42,144 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-15 22:05:42,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-15 22:05:42,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-15 22:05:42,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-15 22:05:43,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-15 22:05:53,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-15 22:05:54,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `ref_vital_signs` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-16 10:00:28,938 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-16 10:00:30,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-16 10:00:32,791 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-16 10:00:34,918 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-16 10:00:35,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-16 10:00:36,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:00:36,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `ref_vital_signs` varchar(140)
2025-06-16 10:00:37,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-16 10:00:38,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:00:39,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:00:40,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-16 10:00:40,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0
2025-06-16 10:00:41,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-16 10:00:41,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-16 10:00:42,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-16 10:00:48,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `patient` varchar(140), MODIFY `price_list` varchar(140)
2025-06-16 10:00:48,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-16 10:00:49,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-16 10:00:49,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-16 10:00:49,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-16 10:00:50,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-16 10:00:50,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name_index`, DROP INDEX `lab_test_code`
2025-06-16 10:00:50,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:00:51,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-16 10:00:53,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-16 10:00:53,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-16 10:00:54,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:00:55,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `ref_vital_signs` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `appointment_type` varchar(140)
2025-06-16 10:00:56,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-16 10:00:56,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` ADD COLUMN `points_of_care` varchar(140)
2025-06-16 10:00:56,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-16 10:00:57,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` ADD COLUMN `points_of_care` varchar(140) default 'Phisiotherapy'
2025-06-16 10:00:57,922 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:00:58,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-16 10:00:59,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9)
2025-06-16 10:00:59,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-16 10:00:59,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-16 10:01:00,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-16 10:01:06,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `patient` varchar(140), MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `price_list` varchar(140)
2025-06-16 10:01:06,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Examination Template` ADD COLUMN `points_of_care` varchar(140) default 'Radiology'
2025-06-16 10:01:06,630 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Examination Template` MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:01:07,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` ADD COLUMN `points_of_care` varchar(140) default 'Pharmacy'
2025-06-16 10:01:07,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:01:07,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` ADD INDEX `points_of_care_index`(`points_of_care`)
2025-06-16 10:01:07,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-16 10:01:07,697 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-16 10:01:07,940 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-16 10:01:07,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` ADD INDEX `medical_code_standard_index`(`medical_code_standard`)
2025-06-16 10:01:08,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-16 10:01:08,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD COLUMN `points_of_care` varchar(140) default 'Laboratory'
2025-06-16 10:01:08,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-16 10:01:08,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-16 10:01:08,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-16 10:01:09,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-16 10:01:10,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-06-16 10:01:20,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-16 10:01:20,712 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `ref_vital_signs` varchar(140), MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0
