2025-06-19 12:08:40,343 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for axessio
2025-06-19 12:08:40,348 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for axessio
2025-06-19 12:08:40,351 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:08:40,354 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for axessio
2025-06-19 12:08:40,357 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:08:40,360 ERROR scheduler Skipped queueing erpnext_germany.tasks.all because it was found in queue for axessio
2025-06-19 12:08:40,364 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 12:08:40,367 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily because it was found in queue for axessio
2025-06-19 12:08:40,370 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:08:40,374 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:08:40,377 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for axessio
2025-06-19 12:08:40,381 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.workday.workday.generate_workdays_scheduled_job because it was found in queue for axessio
2025-06-19 12:08:40,386 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:08:40,389 ERROR scheduler Skipped queueing active_users.utils.update.auto_check_for_update because it was found in queue for axessio
2025-06-19 12:08:40,393 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 12:08:40,397 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.sms_notification.sms_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:08:40,402 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_all because it was found in queue for axessio
2025-06-19 12:08:40,405 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for axessio
2025-06-19 12:08:40,408 ERROR scheduler Skipped queueing invoice_schedule_cron because it was found in queue for axessio
2025-06-19 12:08:40,412 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for axessio
2025-06-19 12:08:40,417 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:08:40,423 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for axessio
2025-06-19 12:08:40,427 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:08:40,432 ERROR scheduler Skipped queueing insights.insights.doctype.insights_alert.insights_alert.send_alerts because it was found in queue for axessio
2025-06-19 12:08:40,435 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for axessio
2025-06-19 12:08:40,438 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 12:08:40,441 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for axessio
2025-06-19 12:08:40,444 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:08:40,448 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.hr_addon_settings.hr_addon_settings.send_work_anniversary_notification because it was found in queue for axessio
2025-06-19 12:08:40,452 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for axessio
2025-06-19 12:08:40,456 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:08:40,459 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for axessio
2025-06-19 12:08:40,464 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:08:40,468 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for axessio
2025-06-19 12:08:40,472 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for axessio
2025-06-19 12:08:40,475 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for axessio
2025-06-19 12:08:40,478 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.intraday_sync_ebics because it was found in queue for axessio
2025-06-19 12:08:40,481 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:08:40,484 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 12:08:40,487 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:08:40,491 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:08:40,494 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:08:40,497 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:08:40,500 ERROR scheduler Skipped queueing erpnext_telegram_integration.extra_notifications.doctype.date_notification.date_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:08:40,503 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:08:40,507 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:08:40,510 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for axessio
2025-06-19 12:08:40,513 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:08:40,516 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:08:40,519 ERROR scheduler Skipped queueing drive.api.files.auto_delete_from_trash because it was found in queue for axessio
2025-06-19 12:08:40,522 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:08:40,525 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for axessio
2025-06-19 12:08:40,528 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for axessio
2025-06-19 12:08:40,531 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 12:08:40,534 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:08:40,537 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:08:40,541 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:08:40,545 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for axessio
2025-06-19 12:08:40,549 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for axessio
2025-06-19 12:08:40,554 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:08:40,557 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:08:40,560 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for axessio
2025-06-19 12:08:40,563 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 12:08:40,566 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for axessio
2025-06-19 12:08:40,569 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:08:40,573 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for axessio
2025-06-19 12:08:40,576 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for axessio
2025-06-19 12:08:40,579 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:08:40,583 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:08:40,586 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:08:40,590 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for axessio
2025-06-19 12:08:40,593 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for axessio
2025-06-19 12:08:40,597 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:08:40,602 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for axessio
2025-06-19 12:08:40,606 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:08:40,613 ERROR scheduler Skipped queueing drive.api.files.clear_deleted_files because it was found in queue for axessio
2025-06-19 12:08:40,617 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:08:40,621 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:08:40,624 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:08:40,627 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:08:40,629 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly because it was found in queue for axessio
2025-06-19 12:08:40,632 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:08:40,635 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:08:40,640 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:08:40,644 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:08:40,647 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.telegram_notification.telegram_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:08:40,650 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:08:40,654 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.sync_all_accounts_and_transactions because it was found in queue for axessio
2025-06-19 12:08:40,658 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly because it was found in queue for axessio
2025-06-19 12:08:40,661 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for axessio
2025-06-19 12:08:40,665 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for axessio
2025-06-19 12:08:40,670 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 12:08:40,673 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:08:40,678 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for axessio
2025-06-19 12:08:40,681 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for axessio
2025-06-19 12:08:40,685 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for axessio
2025-06-19 12:08:40,689 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for axessio
2025-06-19 12:09:41,774 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:09:41,779 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:09:41,784 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for axessio
2025-06-19 12:09:41,791 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:09:41,798 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:09:41,803 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:09:41,810 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for axessio
2025-06-19 12:09:41,827 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:09:41,841 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:09:41,846 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:09:41,851 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.intraday_sync_ebics because it was found in queue for axessio
2025-06-19 12:09:41,854 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for axessio
2025-06-19 12:09:41,862 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly because it was found in queue for axessio
2025-06-19 12:09:41,866 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:09:41,872 ERROR scheduler Skipped queueing insights.insights.doctype.insights_alert.insights_alert.send_alerts because it was found in queue for axessio
2025-06-19 12:09:41,893 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for axessio
2025-06-19 12:09:41,898 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:09:41,905 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for axessio
2025-06-19 12:09:41,910 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:09:41,917 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly because it was found in queue for axessio
2025-06-19 12:09:41,923 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:09:41,927 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.telegram_notification.telegram_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:09:41,932 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:09:41,937 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for axessio
2025-06-19 12:09:41,943 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily because it was found in queue for axessio
2025-06-19 12:09:41,948 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:09:41,957 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for axessio
2025-06-19 12:09:41,962 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:09:41,967 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:09:41,970 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.sms_notification.sms_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:09:41,974 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:09:41,977 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:09:41,982 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:09:41,986 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for axessio
2025-06-19 12:09:41,990 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for axessio
2025-06-19 12:09:41,995 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for axessio
2025-06-19 12:09:41,999 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 12:09:42,003 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:09:42,008 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 12:09:42,013 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:09:42,018 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for axessio
2025-06-19 12:09:42,023 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:09:42,026 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for axessio
2025-06-19 12:09:42,031 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for axessio
2025-06-19 12:09:42,036 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for axessio
2025-06-19 12:09:42,041 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:09:42,046 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:09:42,050 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for axessio
2025-06-19 12:09:42,054 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:09:42,058 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:09:42,063 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:09:42,069 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for axessio
2025-06-19 12:09:42,074 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.workday.workday.generate_workdays_scheduled_job because it was found in queue for axessio
2025-06-19 12:09:42,080 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:09:42,085 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for axessio
2025-06-19 12:09:42,090 ERROR scheduler Skipped queueing drive.api.files.clear_deleted_files because it was found in queue for axessio
2025-06-19 12:09:42,095 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 12:09:42,100 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for axessio
2025-06-19 12:09:42,104 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:09:42,108 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for axessio
2025-06-19 12:09:42,112 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for axessio
2025-06-19 12:09:42,116 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for axessio
2025-06-19 12:09:42,120 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 12:09:42,125 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for axessio
2025-06-19 12:09:42,130 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for axessio
2025-06-19 12:09:42,138 ERROR scheduler Skipped queueing erpnext_telegram_integration.extra_notifications.doctype.date_notification.date_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:09:42,146 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:09:42,149 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:09:42,156 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 12:09:42,159 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 12:09:42,162 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:09:42,165 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 12:09:42,168 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:09:42,172 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_all because it was found in queue for axessio
2025-06-19 12:09:42,175 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for axessio
2025-06-19 12:09:42,179 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for axessio
2025-06-19 12:09:42,189 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:09:42,192 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for axessio
2025-06-19 12:09:42,195 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for axessio
2025-06-19 12:09:42,198 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for axessio
2025-06-19 12:09:42,202 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.sync_all_accounts_and_transactions because it was found in queue for axessio
2025-06-19 12:09:42,206 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:09:42,209 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:09:42,213 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 12:09:42,216 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:09:42,220 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for axessio
2025-06-19 12:09:42,228 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:09:42,231 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for axessio
2025-06-19 12:09:42,235 ERROR scheduler Skipped queueing erpnext_germany.tasks.all because it was found in queue for axessio
2025-06-19 12:09:42,244 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for axessio
2025-06-19 12:09:42,248 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:09:42,253 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for axessio
2025-06-19 12:09:42,258 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:09:42,263 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for axessio
2025-06-19 12:09:42,267 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for axessio
2025-06-19 12:09:42,271 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:09:42,274 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:09:42,277 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:09:42,280 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for axessio
2025-06-19 12:09:42,284 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:09:42,288 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:09:42,292 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:09:42,299 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:09:42,302 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for axessio
2025-06-19 12:09:42,306 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:09:42,309 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:09:42,312 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for axessio
2025-06-19 12:09:42,317 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for axessio
2025-06-19 12:09:42,324 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for axessio
2025-06-19 12:09:42,329 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:09:42,334 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for axessio
2025-06-19 12:09:42,343 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for axessio
2025-06-19 12:16:51,073 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:16:51,178 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:16:51,346 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:16:51,416 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:16:51,478 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:16:51,484 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:16:51,513 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:16:51,676 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:16:51,730 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:16:51,834 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:16:51,933 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:16:52,101 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:16:52,197 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:16:52,202 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:16:52,218 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:16:52,262 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:16:52,266 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:16:52,271 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:16:52,298 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:16:52,303 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:16:52,313 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:16:52,323 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:16:52,379 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:16:52,397 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:16:52,415 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:16:52,419 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:16:52,424 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:16:52,455 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:16:52,470 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:16:52,516 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:16:52,526 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:16:52,537 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:16:52,558 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:16:52,576 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:16:52,581 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:16:52,589 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:16:52,593 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:16:52,603 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:16:52,630 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:16:52,642 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:16:52,665 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:16:52,722 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:17:55,238 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:17:55,247 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:17:55,250 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 12:17:55,253 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:17:55,266 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:17:55,276 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 12:17:55,284 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:17:55,289 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:17:55,298 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:17:55,307 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:17:55,312 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for axessio
2025-06-19 12:17:55,324 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:17:55,332 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:17:55,338 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:17:55,362 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:17:55,365 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for axessio
2025-06-19 12:17:55,368 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:17:55,387 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:17:55,391 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:17:55,401 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:17:55,409 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:17:55,424 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:17:55,429 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:17:55,451 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:17:55,469 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:17:55,472 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:17:55,476 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:17:55,483 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:17:55,486 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:17:55,489 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:17:55,492 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:17:55,528 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:17:55,540 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:17:55,547 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:17:55,566 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:17:55,581 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:17:55,595 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:17:55,607 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:17:55,632 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for axessio
2025-06-19 12:17:55,639 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:17:55,648 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:17:55,659 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for axessio
2025-06-19 12:17:55,663 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:17:55,686 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:17:55,690 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:17:55,694 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:17:55,721 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:17:55,726 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:17:55,730 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:17:55,745 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:17:55,753 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:25:28,590 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:26:30,366 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:27:31,263 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:28:33,003 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:29:34,008 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:30:35,115 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:31:35,690 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:31:35,693 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:31:35,697 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.sms_notification.sms_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:31:35,701 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly because it was found in queue for axessio
2025-06-19 12:31:35,704 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:31:35,708 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for axessio
2025-06-19 12:31:35,712 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for axessio
2025-06-19 12:31:35,715 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for axessio
2025-06-19 12:31:35,719 ERROR scheduler Skipped queueing erpnext_germany.tasks.all because it was found in queue for axessio
2025-06-19 12:31:35,723 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:31:35,727 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for axessio
2025-06-19 12:31:35,731 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:31:35,734 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:31:35,739 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:31:35,742 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for axessio
2025-06-19 12:31:35,747 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 12:31:35,751 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for axessio
2025-06-19 12:31:35,754 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:31:35,760 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:31:35,764 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for axessio
2025-06-19 12:31:35,767 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:31:35,771 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for axessio
2025-06-19 12:31:35,774 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for axessio
2025-06-19 12:31:35,778 ERROR scheduler Skipped queueing drive.api.files.auto_delete_from_trash because it was found in queue for axessio
2025-06-19 12:31:35,782 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for axessio
2025-06-19 12:31:35,786 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:31:35,789 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for axessio
2025-06-19 12:31:35,793 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for axessio
2025-06-19 12:31:35,796 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for axessio
2025-06-19 12:31:35,800 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:31:35,804 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 12:31:35,808 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for axessio
2025-06-19 12:31:35,811 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:31:35,815 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for axessio
2025-06-19 12:31:35,818 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for axessio
2025-06-19 12:31:35,822 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.hr_addon_settings.hr_addon_settings.send_work_anniversary_notification because it was found in queue for axessio
2025-06-19 12:31:35,826 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 12:31:35,829 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for axessio
2025-06-19 12:31:35,833 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for axessio
2025-06-19 12:31:35,836 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:31:35,840 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:31:35,843 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for axessio
2025-06-19 12:31:35,847 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:31:35,851 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_all because it was found in queue for axessio
2025-06-19 12:31:35,854 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.telegram_notification.telegram_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:31:35,859 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:31:35,864 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:31:35,870 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for axessio
2025-06-19 12:31:35,874 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.workday.workday.generate_workdays_scheduled_job because it was found in queue for axessio
2025-06-19 12:31:35,878 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for axessio
2025-06-19 12:31:35,884 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for axessio
2025-06-19 12:31:35,888 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:31:35,891 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:31:35,895 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for axessio
2025-06-19 12:31:35,899 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:31:35,902 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for axessio
2025-06-19 12:31:35,906 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:31:35,910 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:31:35,913 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for axessio
2025-06-19 12:31:35,918 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for axessio
2025-06-19 12:31:35,922 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for axessio
2025-06-19 12:31:35,925 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:31:35,929 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:31:35,933 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for axessio
2025-06-19 12:31:35,937 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:31:35,941 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for axessio
2025-06-19 12:31:35,944 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for axessio
2025-06-19 12:31:35,948 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for axessio
2025-06-19 12:31:35,952 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for axessio
2025-06-19 12:31:35,956 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 12:31:35,962 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:31:35,966 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for axessio
2025-06-19 12:31:35,970 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for axessio
2025-06-19 12:31:35,974 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily because it was found in queue for axessio
2025-06-19 12:31:35,978 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:31:35,982 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for axessio
2025-06-19 12:31:35,986 ERROR scheduler Skipped queueing print_designer.install.setup_chromium because it was found in queue for axessio
2025-06-19 12:31:35,990 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:31:35,994 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:31:35,998 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for axessio
2025-06-19 12:31:36,004 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:31:36,008 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for axessio
2025-06-19 12:31:36,011 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:31:36,016 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for axessio
2025-06-19 12:31:36,019 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:31:36,024 ERROR scheduler Skipped queueing insights.insights.doctype.insights_alert.insights_alert.send_alerts because it was found in queue for axessio
2025-06-19 12:31:36,028 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for axessio
2025-06-19 12:31:36,032 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:31:36,035 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:31:36,039 ERROR scheduler Skipped queueing erpnext_telegram_integration.extra_notifications.doctype.date_notification.date_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:31:36,042 ERROR scheduler Skipped queueing invoice_schedule_cron because it was found in queue for axessio
2025-06-19 12:31:36,046 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:31:36,049 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:31:36,055 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:31:36,060 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:31:36,064 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly because it was found in queue for axessio
2025-06-19 12:31:36,068 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:31:36,072 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:31:36,077 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for axessio
2025-06-19 12:31:36,081 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for axessio
2025-06-19 12:31:36,085 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:31:36,089 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:31:36,092 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for axessio
2025-06-19 12:31:36,096 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:31:36,100 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for axessio
2025-06-19 12:31:36,104 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for axessio
2025-06-19 12:31:36,108 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:31:36,112 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 12:31:36,118 ERROR scheduler Skipped queueing active_users.utils.update.auto_check_for_update because it was found in queue for axessio
2025-06-19 12:31:36,122 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:31:36,127 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 12:31:36,131 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:31:36,139 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.sync_all_accounts_and_transactions because it was found in queue for axessio
2025-06-19 12:31:36,143 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for axessio
2025-06-19 12:31:36,147 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 12:31:36,150 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for axessio
2025-06-19 12:31:36,154 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:31:36,158 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.intraday_sync_ebics because it was found in queue for axessio
2025-06-19 12:31:36,161 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 12:31:36,165 ERROR scheduler Skipped queueing drive.api.files.clear_deleted_files because it was found in queue for axessio
2025-06-19 12:31:36,168 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:31:36,172 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for axessio
2025-06-19 12:31:36,176 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:31:36,179 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:32:36,697 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:33:38,043 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:34:38,904 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:35:40,311 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:36:41,517 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:37:42,518 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:38:43,399 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:39:44,534 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:40:45,454 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:41:46,515 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:42:47,515 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:43:48,753 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:46:52,129 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for axessio
2025-06-19 12:46:52,140 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:46:52,144 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for axessio
2025-06-19 12:46:52,147 ERROR scheduler Skipped queueing print_designer.install.setup_chromium because it was found in queue for axessio
2025-06-19 12:46:52,151 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for axessio
2025-06-19 12:46:52,178 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:46:52,181 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:46:52,184 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:46:52,187 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:46:52,190 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 12:46:52,206 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:46:52,226 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 12:46:52,229 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:46:52,232 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:46:52,245 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:46:52,248 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:46:52,251 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:46:52,254 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:46:52,257 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:46:52,270 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:46:52,273 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:46:52,284 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:46:52,288 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:46:52,295 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:46:52,302 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:46:52,305 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:46:52,312 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for axessio
2025-06-19 12:46:52,316 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:46:52,323 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for axessio
2025-06-19 12:46:52,327 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for axessio
2025-06-19 12:46:52,337 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:46:52,344 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:46:52,358 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:46:52,361 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:46:52,400 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:46:52,414 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for axessio
2025-06-19 12:46:52,418 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:46:52,422 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:46:52,430 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for axessio
2025-06-19 12:46:52,433 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for axessio
2025-06-19 12:46:52,443 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:46:52,450 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 12:46:52,453 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:46:52,458 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:46:52,461 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:46:52,464 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:46:52,467 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:46:52,470 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for axessio
2025-06-19 12:46:52,478 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:46:52,491 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:46:52,497 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:46:52,510 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:46:52,513 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 12:46:52,516 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 12:46:52,523 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:46:52,527 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:46:52,545 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:46:52,548 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:46:52,558 ERROR scheduler Skipped queueing active_users.utils.update.auto_check_for_update because it was found in queue for axessio
2025-06-19 12:46:52,569 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.sms_notification.sms_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:46:52,572 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:46:52,579 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:46:52,582 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:46:52,589 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 13:01:07,576 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 13:01:07,579 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 13:01:07,582 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 13:01:07,595 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 13:01:07,598 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 13:01:07,600 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 13:01:07,610 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 13:01:07,613 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 13:01:07,635 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 13:01:07,638 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 13:01:07,690 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 13:01:07,693 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 13:01:07,697 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 13:01:07,706 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 13:01:07,720 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 13:01:07,724 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 13:01:07,734 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 13:01:07,759 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 13:01:07,781 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 13:01:07,784 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 13:01:07,792 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 13:01:07,813 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 13:01:07,849 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 13:01:07,853 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 13:01:07,856 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 13:01:07,867 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 13:01:07,869 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 13:01:07,875 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 13:01:07,881 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 13:01:07,887 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 13:01:07,906 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 13:01:07,916 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 13:01:07,926 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 13:01:07,946 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 13:01:07,965 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 13:01:07,979 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 13:01:08,017 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 13:01:08,031 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 13:01:08,039 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 13:01:08,046 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 13:01:08,049 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 13:01:08,058 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 13:03:09,403 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 13:03:09,407 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 13:03:09,410 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 13:03:09,414 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 13:03:09,417 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 13:03:09,420 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 13:03:09,423 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 13:03:09,428 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 13:03:09,432 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 13:03:09,435 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 13:03:09,438 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for axessio
2025-06-19 13:03:09,441 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 13:03:09,444 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for axessio
2025-06-19 13:03:09,449 ERROR scheduler Skipped queueing insights.insights.doctype.insights_alert.insights_alert.send_alerts because it was found in queue for axessio
2025-06-19 13:03:09,452 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for axessio
2025-06-19 13:03:09,455 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 13:03:09,458 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for axessio
2025-06-19 13:03:09,460 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for axessio
2025-06-19 13:03:09,463 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.telegram_notification.telegram_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 13:03:09,467 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for axessio
2025-06-19 13:03:09,470 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 13:03:09,472 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for axessio
2025-06-19 13:03:09,475 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for axessio
2025-06-19 13:03:09,479 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 13:03:09,482 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 13:03:09,486 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 13:03:09,489 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 13:03:09,493 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 13:03:09,496 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 13:03:09,499 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 13:03:09,504 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for axessio
2025-06-19 13:03:09,508 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 13:03:09,512 ERROR scheduler Skipped queueing drive.api.files.clear_deleted_files because it was found in queue for axessio
2025-06-19 13:03:09,516 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 13:03:09,520 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 13:03:09,523 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for axessio
2025-06-19 13:03:09,527 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 13:03:09,530 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 13:03:09,533 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for axessio
2025-06-19 13:03:09,537 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for axessio
2025-06-19 13:03:09,540 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 13:03:09,543 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for axessio
2025-06-19 13:03:09,547 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 13:03:09,550 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 13:03:09,555 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly because it was found in queue for axessio
2025-06-19 13:03:09,558 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for axessio
2025-06-19 13:03:09,562 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.sync_all_accounts_and_transactions because it was found in queue for axessio
2025-06-19 13:03:09,566 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 13:03:09,570 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 13:03:09,573 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 13:03:09,576 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for axessio
2025-06-19 13:03:09,580 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 13:03:09,583 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
