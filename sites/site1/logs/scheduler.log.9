2025-06-15 15:57:49,841 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 15:57:49,842 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 15:57:49,843 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for site1
2025-06-15 15:57:49,844 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for site1
2025-06-15 15:57:49,845 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 15:57:49,846 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 15:57:49,847 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 15:57:49,847 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for site1
2025-06-15 15:57:49,848 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for site1
2025-06-15 15:57:49,849 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 15:57:49,850 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for site1
2025-06-15 15:57:49,851 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 15:57:49,853 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 15:57:49,854 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 15:57:49,855 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 15:57:49,856 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for site1
2025-06-15 15:57:49,857 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for site1
2025-06-15 15:57:49,857 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for site1
2025-06-15 15:57:49,858 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 15:57:49,859 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 15:57:49,860 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for site1
2025-06-15 15:57:49,861 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for site1
2025-06-15 15:57:49,861 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for site1
2025-06-15 15:58:50,080 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for site1
2025-06-15 15:58:50,081 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 15:58:50,082 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 15:58:50,083 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for site1
2025-06-15 15:58:50,083 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 15:58:50,084 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for site1
2025-06-15 15:58:50,085 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 15:58:50,086 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for site1
2025-06-15 15:58:50,087 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 15:58:50,088 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 15:58:50,088 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for site1
2025-06-15 15:58:50,089 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 15:58:50,090 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for site1
2025-06-15 15:58:50,091 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 15:58:50,092 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 15:58:50,092 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for site1
2025-06-15 15:58:50,093 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 15:58:50,094 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for site1
2025-06-15 15:58:50,095 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for site1
2025-06-15 15:58:50,096 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 15:58:50,097 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 15:58:50,097 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 15:58:50,098 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 15:58:50,099 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 15:58:50,100 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for site1
2025-06-15 15:58:50,101 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 15:58:50,102 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for site1
2025-06-15 15:58:50,102 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 15:58:50,103 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for site1
2025-06-15 15:58:50,104 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 15:58:50,105 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 15:58:50,106 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 15:58:50,107 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 15:58:50,108 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for site1
2025-06-15 15:58:50,109 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 15:58:50,109 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for site1
2025-06-15 15:58:50,110 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 15:58:50,111 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for site1
2025-06-15 15:58:50,112 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 15:58:50,113 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 15:58:50,113 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for site1
2025-06-15 15:58:50,114 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for site1
2025-06-15 15:58:50,115 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for site1
2025-06-15 15:58:50,116 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 15:58:50,117 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for site1
2025-06-15 15:58:50,117 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for site1
2025-06-15 15:58:50,118 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 15:58:50,119 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 15:58:50,121 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 15:58:50,121 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for site1
2025-06-15 15:58:50,122 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 15:58:50,123 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for site1
2025-06-15 15:58:50,124 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for site1
2025-06-15 15:58:50,125 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 15:58:50,125 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 15:58:50,126 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 15:58:50,127 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 15:58:50,128 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 15:58:50,129 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for site1
2025-06-15 15:58:50,130 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 15:58:50,130 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 15:58:50,131 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 15:58:50,132 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 15:58:50,133 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 15:58:50,134 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 15:58:50,135 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 15:58:50,135 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for site1
2025-06-15 15:58:50,137 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for site1
2025-06-15 15:58:50,138 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 15:58:50,139 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 15:58:50,140 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for site1
2025-06-15 15:58:50,140 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for site1
2025-06-15 15:58:50,142 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 15:58:50,142 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 15:58:50,143 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for site1
2025-06-15 15:58:50,144 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for site1
2025-06-15 15:58:50,145 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for site1
2025-06-15 15:58:50,146 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 15:58:50,146 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 15:58:50,147 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for site1
2025-06-15 15:58:50,148 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for site1
2025-06-15 15:58:50,149 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for site1
2025-06-15 15:58:50,150 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 15:58:50,150 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 15:58:50,151 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for site1
2025-06-15 15:58:50,152 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 15:58:50,153 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 15:58:50,154 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for site1
2025-06-15 15:58:50,155 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 15:58:50,155 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for site1
2025-06-15 15:58:50,156 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for site1
2025-06-15 15:58:50,158 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for site1
2025-06-15 15:58:50,159 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 15:58:50,159 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 15:58:50,160 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for site1
2025-06-15 15:58:50,161 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 15:58:50,162 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 15:58:50,163 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for site1
2025-06-15 15:58:50,163 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 15:58:50,164 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for site1
2025-06-15 15:58:50,165 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for site1
2025-06-15 15:58:50,166 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 15:58:50,167 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 15:58:50,168 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for site1
2025-06-15 15:58:50,169 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 15:58:50,170 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for site1
2025-06-15 15:58:50,171 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 15:58:50,172 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 15:59:50,452 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 15:59:50,453 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 15:59:50,457 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 15:59:50,459 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 15:59:50,461 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 15:59:50,464 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 15:59:50,465 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 15:59:50,466 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 15:59:50,467 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 15:59:50,467 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 15:59:50,469 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 15:59:50,470 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 15:59:50,472 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 15:59:50,472 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 15:59:50,473 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 15:59:50,477 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 15:59:50,478 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 15:59:50,479 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 15:59:50,480 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 15:59:50,482 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 15:59:50,487 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 15:59:50,488 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 15:59:50,489 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 15:59:50,490 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 15:59:50,495 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 15:59:50,496 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 15:59:50,497 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 15:59:50,499 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 15:59:50,502 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 15:59:50,504 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 15:59:50,508 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 15:59:50,509 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 15:59:50,509 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 15:59:50,511 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 15:59:50,512 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 15:59:50,514 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 15:59:50,515 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 15:59:50,516 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 15:59:50,520 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:08:37,685 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:08:37,704 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:08:37,708 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:08:37,737 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:08:37,741 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:08:37,756 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:08:37,763 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:08:37,775 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:08:37,789 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:08:37,805 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:08:37,824 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:08:37,835 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:08:37,849 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:08:37,871 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:08:37,878 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:08:37,888 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:08:37,892 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:08:37,897 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:08:37,901 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:08:37,906 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:08:37,936 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:08:37,947 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:08:37,961 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:08:37,965 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:08:37,969 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:08:37,974 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:08:37,983 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:08:37,992 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:08:38,014 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:08:38,026 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:08:38,032 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:08:38,036 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:08:38,039 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:08:38,043 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:08:38,053 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:08:38,057 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:08:38,063 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:08:38,068 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:08:38,072 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:09:39,162 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:09:39,166 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:09:39,170 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:09:39,176 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 21:09:39,179 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:09:39,184 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:09:39,187 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:09:39,192 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:09:39,199 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 21:09:39,209 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:09:39,215 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:09:39,220 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:09:39,234 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:09:39,243 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:09:39,250 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:09:39,258 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:09:39,263 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:09:39,268 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:09:39,279 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 21:09:39,283 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:09:39,289 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 21:09:39,294 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:09:39,313 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:09:39,318 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:09:39,340 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 21:09:39,350 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:09:39,353 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 21:09:39,359 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 21:09:39,365 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:09:39,373 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:09:39,377 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:09:39,384 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:09:39,388 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:09:39,401 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:09:39,407 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 21:09:39,411 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:09:39,415 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:09:39,423 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 21:09:39,440 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:09:39,445 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 21:09:39,450 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:09:39,458 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 21:09:39,466 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:09:39,478 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:09:39,484 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 21:09:39,496 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 21:09:39,500 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:09:39,505 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:09:39,510 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:09:39,518 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:09:39,523 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 21:09:39,529 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 21:09:39,533 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:09:39,538 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 21:09:39,544 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 21:09:39,550 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:09:39,557 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:09:39,563 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 21:09:39,571 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:09:39,579 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:09:39,585 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 21:09:39,590 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:09:39,606 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:10:39,628 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:10:39,634 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:10:39,637 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 21:10:39,641 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:10:39,648 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:10:39,653 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:10:39,657 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:10:39,663 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:10:39,671 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:10:39,674 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 21:10:39,677 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:10:39,685 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 21:10:39,688 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:10:39,691 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:10:39,699 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:10:39,703 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 21:10:39,706 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:10:39,710 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:10:39,714 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 21:10:39,721 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 21:10:39,725 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 21:10:39,735 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 21:10:39,747 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:10:39,753 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:10:39,757 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:10:39,761 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:10:39,766 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:10:39,771 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:10:39,779 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 21:10:39,786 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 21:10:39,790 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:10:39,798 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:10:39,802 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 21:10:39,807 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:10:39,811 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:10:39,820 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:10:39,824 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:10:39,828 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:10:39,831 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:10:39,837 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:10:39,845 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:10:39,848 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 21:10:39,853 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:10:39,856 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:10:39,862 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 21:10:39,866 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:10:39,870 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:10:39,873 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:10:39,877 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:10:39,880 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 21:10:39,884 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 21:10:39,890 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:10:39,896 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 21:10:39,900 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 21:10:39,903 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:10:39,911 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 21:10:39,917 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:10:39,921 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 21:10:39,938 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:10:39,946 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:10:39,951 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:10:39,958 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:10:39,964 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:11:41,583 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:11:41,590 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 21:11:41,595 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 21:11:41,599 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 21:11:41,603 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:11:41,613 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 21:11:41,621 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 21:11:41,624 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:11:41,628 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 21:11:41,634 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:11:41,643 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:11:41,647 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:11:41,651 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:11:41,655 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:11:41,659 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 21:11:41,664 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:11:41,667 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:11:41,671 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:11:41,675 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:11:41,679 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:11:41,683 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:11:41,694 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:11:41,699 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:11:41,711 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:11:41,715 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 21:11:41,719 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:11:41,725 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:11:41,734 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 21:11:41,741 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:11:41,748 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:11:41,754 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:11:41,758 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:11:41,763 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:11:41,767 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 21:11:41,771 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 21:11:41,775 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:11:41,779 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:11:41,786 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:11:41,789 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 21:11:41,793 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:11:41,803 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:11:41,807 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:11:41,815 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:11:41,819 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:11:41,825 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:11:41,831 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:11:41,835 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 21:11:41,839 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 21:11:41,844 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 21:11:41,850 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:11:41,856 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:11:41,860 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:11:41,870 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:11:41,876 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 21:11:41,879 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 21:11:41,887 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:11:41,893 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:11:41,896 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 21:11:41,905 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:11:41,919 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:11:41,923 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:11:41,936 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 21:11:41,939 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:12:42,615 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 21:12:42,622 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:12:42,628 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:12:42,634 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:12:42,643 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:12:42,647 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:12:42,650 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:12:42,662 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 21:12:42,665 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:12:42,678 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 21:12:42,682 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:12:42,689 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 21:12:42,693 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:12:42,697 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:12:42,702 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:12:42,709 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:12:42,714 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 21:12:42,723 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:12:42,727 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:12:42,731 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:12:42,736 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 21:12:42,741 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:12:42,751 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:12:42,755 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:12:42,760 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:12:42,766 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:12:42,769 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:12:42,777 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:12:42,781 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:12:42,787 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:12:42,795 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:12:42,799 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:12:42,803 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:12:42,811 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:12:42,815 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:12:42,819 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:12:42,823 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:12:42,830 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:12:42,842 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 21:12:42,846 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 21:12:42,854 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 21:12:42,868 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 21:12:42,871 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:12:42,875 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 21:12:42,878 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 21:12:42,889 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:12:42,895 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:12:42,899 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 21:12:42,903 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:12:42,907 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 21:12:42,911 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:12:42,916 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 21:12:42,924 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:12:42,932 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 21:12:42,940 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:12:42,950 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:12:42,954 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 21:12:42,960 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 21:12:42,964 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 21:12:42,974 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:12:42,981 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:12:42,988 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:12:42,993 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:13:44,228 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:13:44,232 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:13:44,239 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 21:13:44,249 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:13:44,258 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 21:13:44,264 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:13:44,268 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:13:44,276 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:13:44,282 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:13:44,288 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 21:13:44,292 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:13:44,296 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:13:44,300 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:13:44,307 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 21:13:44,314 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:13:44,320 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 21:13:44,324 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:13:44,328 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:13:44,333 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 21:13:44,338 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:13:44,348 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 21:13:44,353 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:13:44,361 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 21:13:44,367 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:13:44,379 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:13:44,383 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 21:13:44,387 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:13:44,391 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 21:13:44,397 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:13:44,403 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:13:44,409 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 21:13:44,412 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:13:44,419 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:13:44,423 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:13:44,431 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:13:44,435 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:13:44,446 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:13:44,450 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:13:44,455 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:13:44,459 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 21:13:44,470 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 21:13:44,474 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 21:13:44,478 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:13:44,489 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:13:44,504 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:13:44,511 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:13:44,517 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 21:13:44,523 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 21:13:44,528 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:13:44,534 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:13:44,545 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 21:13:44,549 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:13:44,555 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:13:44,560 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:13:44,564 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:13:44,568 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:13:44,577 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:13:44,585 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:13:44,588 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 21:13:44,606 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:13:44,609 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:13:44,613 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 21:13:44,620 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:14:46,420 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:14:46,423 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:14:46,426 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:14:46,431 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:14:46,438 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:14:46,441 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:14:46,447 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:14:46,455 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 21:14:46,465 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 21:14:46,471 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:14:46,474 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 21:14:46,478 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 21:14:46,481 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:14:46,485 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:14:46,489 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 21:14:46,496 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:14:46,500 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 21:14:46,505 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:14:46,509 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:14:46,512 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:14:46,516 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:14:46,520 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 21:14:46,524 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:14:46,527 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:14:46,531 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 21:14:46,534 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 21:14:46,540 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 21:14:46,543 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:14:46,547 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 21:14:46,555 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 21:14:46,561 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:14:46,568 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 21:14:46,578 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:14:46,584 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 21:14:46,588 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 21:14:46,599 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:14:46,603 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:14:46,608 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 21:14:46,635 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 21:14:46,647 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 21:14:46,650 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:14:46,655 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 21:14:46,662 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 21:14:46,667 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 21:14:46,675 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 21:14:46,679 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 21:14:46,687 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 21:14:46,691 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 21:14:46,696 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 21:14:46,700 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 21:14:46,704 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:14:46,707 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 21:14:46,714 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 21:14:46,717 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 21:14:46,725 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 21:14:46,738 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 21:14:46,741 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 21:14:46,744 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:14:46,747 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:14:46,751 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 21:14:46,756 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:14:46,760 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 21:14:46,766 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:15:47,763 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 21:15:47,769 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 21:15:47,778 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:15:47,786 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 21:15:47,803 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 21:15:47,807 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 21:15:47,811 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 21:15:47,816 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 21:15:47,828 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 21:15:47,836 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 21:15:47,839 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 21:15:47,849 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 21:15:47,857 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 21:15:47,862 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 21:15:47,872 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 21:15:47,877 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 21:15:47,882 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 21:15:47,889 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 21:15:47,896 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 21:15:47,900 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 21:15:47,904 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 21:15:47,918 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 21:15:47,933 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 21:15:47,945 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 21:15:47,948 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 21:15:47,951 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 21:15:47,955 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 21:15:47,962 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
