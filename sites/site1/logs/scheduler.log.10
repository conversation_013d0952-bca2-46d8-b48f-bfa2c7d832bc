2025-06-13 08:58:53,135 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for site1
2025-06-13 08:58:53,141 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-13 08:58:53,143 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-13 08:58:53,146 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 08:58:53,148 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-13 08:59:53,746 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 08:59:53,751 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 08:59:53,752 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 08:59:53,774 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 08:59:53,799 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 08:59:53,804 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 08:59:53,818 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 08:59:53,829 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 08:59:53,856 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 08:59:53,873 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 08:59:53,875 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 08:59:53,882 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 08:59:53,885 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 09:00:54,346 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 09:00:54,360 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 09:00:54,363 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 09:00:54,364 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 09:00:54,396 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 09:00:54,402 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 09:00:54,403 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 09:00:54,430 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 09:00:54,442 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 09:00:54,445 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 09:00:54,487 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 09:00:54,492 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 09:00:54,495 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 09:01:54,545 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 09:01:54,558 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 09:01:54,585 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 09:01:54,590 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 09:01:54,605 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 09:01:54,615 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 09:01:54,621 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 09:01:54,636 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 09:01:54,639 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 09:01:54,641 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 09:01:54,650 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 09:01:54,660 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 09:01:54,674 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 09:02:55,334 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 09:02:55,346 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 09:02:55,355 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 09:02:55,367 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 09:02:55,369 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 09:02:55,373 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 09:02:55,379 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 09:02:55,384 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 09:02:55,387 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 09:02:55,400 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 09:02:55,410 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 09:02:55,425 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 09:02:55,431 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 09:03:56,151 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 09:03:56,161 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 09:03:56,167 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 09:03:56,186 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 09:03:56,202 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 09:03:56,208 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 09:03:56,211 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 09:03:56,222 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 09:03:56,226 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 09:03:56,237 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 09:03:56,239 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 09:03:56,256 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 09:03:56,262 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 09:04:56,312 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 09:04:56,326 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 09:04:56,332 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 09:04:56,347 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 09:04:56,352 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 09:04:56,355 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 09:04:56,372 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 09:04:56,387 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 09:04:56,400 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 09:04:56,409 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 09:04:56,414 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 09:04:56,417 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 09:04:56,450 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 10:01:39,244 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 10:01:39,248 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-13 10:01:39,252 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-13 10:01:39,255 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-13 10:01:39,260 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-13 10:01:39,269 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-13 10:01:39,276 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 10:01:39,282 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 10:01:39,292 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 10:01:39,299 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-13 10:01:39,317 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-13 10:01:39,325 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-13 10:01:39,333 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-13 10:01:39,337 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 10:01:39,340 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-13 10:01:39,343 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 10:01:39,349 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-13 10:01:39,353 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-13 10:01:39,356 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-13 10:01:39,359 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-13 10:01:39,381 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-13 10:01:39,386 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-13 10:01:39,398 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 10:01:39,408 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-13 10:01:39,411 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 10:01:39,414 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-13 10:01:39,417 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 10:01:39,419 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 10:01:39,422 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-13 10:01:39,425 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 10:01:39,443 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-13 10:01:39,451 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-13 10:01:39,456 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 10:01:39,474 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 10:02:40,300 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 10:02:40,337 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 10:02:40,343 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 10:02:40,348 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 10:02:40,375 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 10:02:40,400 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 10:02:40,453 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 10:02:40,462 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 10:02:40,465 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 10:02:40,489 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 10:02:40,493 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 10:02:40,498 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 10:02:40,510 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 10:03:40,826 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 10:03:40,904 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 10:03:40,913 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 10:03:40,927 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 10:03:40,946 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 10:03:40,953 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 10:03:40,969 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 10:03:40,981 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 10:03:40,994 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 10:03:41,005 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 10:03:41,015 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 10:03:41,019 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 10:03:41,061 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 10:04:41,867 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 10:04:41,915 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 10:04:41,934 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 10:04:41,942 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 10:04:41,969 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 10:04:41,987 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 10:04:42,017 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 10:04:42,033 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 10:04:42,037 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 10:04:42,072 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 10:04:42,077 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 10:04:42,094 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 10:04:42,103 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 10:05:38,132 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for site1
2025-06-13 10:05:43,062 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 10:05:43,069 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-13 10:05:43,073 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 10:05:43,078 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 10:05:43,080 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 10:05:43,095 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 10:05:43,097 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 10:05:43,104 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-13 10:05:43,111 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 10:05:43,125 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 10:05:43,140 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 10:05:43,144 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 10:05:43,149 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-13 10:05:43,152 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-13 10:05:43,166 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 10:05:43,172 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 10:05:43,177 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 11:01:02,967 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-13 11:01:02,971 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-13 11:01:02,975 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 11:01:02,977 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-13 11:01:02,979 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-13 11:01:02,981 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 11:01:02,982 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 11:01:02,985 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 11:01:02,986 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-13 11:01:02,988 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 11:01:02,998 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-13 11:01:03,001 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-13 11:01:03,005 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 11:01:03,006 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-13 11:01:03,010 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 11:01:03,019 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-13 11:01:03,022 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-13 11:01:03,025 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-13 11:01:03,032 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 11:01:03,033 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-13 11:01:03,040 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 11:01:03,044 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-13 11:01:03,046 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 11:01:03,048 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 11:01:03,049 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-13 11:01:03,051 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-13 11:01:03,058 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 11:01:03,060 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-13 11:01:03,062 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 11:01:03,064 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-13 11:01:03,069 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-13 11:01:03,072 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-13 11:01:03,073 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-13 11:02:03,362 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 11:02:03,365 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 11:02:03,378 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 11:02:03,380 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 11:02:03,383 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 11:02:03,388 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 11:02:03,402 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 11:02:03,412 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 11:02:03,420 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 11:02:03,423 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 11:02:03,429 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 11:02:03,431 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 11:02:03,433 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 12:01:25,336 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-13 12:01:25,343 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-13 12:01:25,345 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-13 12:01:25,347 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 12:01:25,352 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-13 12:01:25,356 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 12:01:25,358 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-13 12:01:25,366 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-13 12:01:25,370 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-13 12:01:25,373 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-13 12:01:25,378 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-13 12:01:25,382 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-13 12:01:25,384 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-13 12:01:25,386 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 12:01:25,392 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 12:01:25,394 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 12:01:25,395 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 12:01:25,399 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-13 12:01:25,401 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 12:01:25,403 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-13 12:01:25,409 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-13 12:01:25,411 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for site1
2025-06-13 12:01:25,414 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-13 12:01:25,417 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-13 12:01:25,422 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 12:01:25,430 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 12:01:25,433 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-13 12:01:25,436 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 12:01:25,439 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 12:01:25,444 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 12:01:25,446 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-13 12:01:25,451 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-13 12:01:25,458 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 12:01:25,475 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-13 12:01:25,478 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-13 12:01:25,480 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-13 12:02:25,951 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 12:02:25,953 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 12:02:25,960 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 12:02:25,973 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 12:02:25,980 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 12:02:25,985 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 12:02:25,991 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 12:02:25,994 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 12:02:26,009 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 12:02:26,024 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 12:02:26,034 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 12:02:26,043 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 12:02:26,049 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 12:03:26,684 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 12:03:26,685 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 12:03:26,691 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 12:03:26,699 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 12:03:26,705 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 12:03:26,710 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 12:03:26,713 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 12:03:26,733 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 12:03:26,747 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 12:03:26,752 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 12:03:26,754 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 12:03:26,763 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 12:03:26,766 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 12:04:27,300 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 12:04:27,312 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 12:04:27,315 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 12:04:27,331 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 12:04:27,346 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 12:04:27,365 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 12:04:27,367 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 12:04:27,369 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 12:04:27,385 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 12:04:27,398 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 12:04:27,412 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 12:04:27,417 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 12:04:27,423 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 12:05:27,910 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 12:05:27,915 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 12:05:27,919 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 12:05:27,926 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 12:05:27,930 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 12:05:27,942 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 12:05:27,945 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 12:05:27,948 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 12:05:27,954 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-13 12:05:27,963 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 12:05:27,977 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 12:05:27,982 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 12:05:27,986 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-13 12:05:27,991 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-13 12:05:27,993 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 12:05:27,997 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 12:05:28,001 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-13 14:01:09,893 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 14:01:09,907 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 14:01:09,913 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 14:01:09,913 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 14:01:09,918 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 14:01:09,921 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 14:01:09,934 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 14:01:09,935 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 14:01:09,936 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 14:01:09,938 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 14:01:09,942 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 14:01:09,946 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 14:02:10,414 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 14:02:10,418 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 14:02:10,419 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 14:02:10,431 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 14:02:10,437 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 14:02:10,445 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 14:02:10,451 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 14:02:10,455 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 14:02:10,459 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 14:02:10,461 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 14:02:10,465 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 14:02:10,469 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 16:01:51,028 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 16:01:51,030 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-13 16:01:51,031 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-13 16:01:51,032 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-13 16:01:51,039 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-13 16:01:51,040 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-13 16:01:51,041 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-13 16:01:51,042 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-13 16:01:51,045 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 16:01:51,048 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-13 16:01:51,049 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 16:01:51,051 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-13 16:01:51,052 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-13 16:01:51,054 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 16:01:51,055 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 16:01:51,055 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 16:01:51,057 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 16:01:51,058 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-13 16:01:51,060 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-13 16:01:51,061 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-13 16:01:51,062 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 16:01:51,064 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-13 16:01:51,067 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 16:01:51,073 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-13 16:01:51,076 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-13 16:01:51,078 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-13 16:01:51,080 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-13 16:01:51,083 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 16:01:51,086 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 16:01:51,087 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-13 16:01:51,090 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-13 16:01:51,091 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-13 16:01:51,095 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-13 16:01:51,098 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 16:01:51,099 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 16:02:51,490 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 16:02:51,494 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 16:02:51,502 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 16:02:51,513 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 16:02:51,518 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 16:02:51,521 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 16:02:51,525 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 16:02:51,526 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 16:02:51,528 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 16:02:51,530 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 16:02:51,537 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 16:02:51,542 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 16:02:51,544 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 16:03:51,651 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 16:03:51,655 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 16:03:51,658 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 16:03:51,661 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 16:03:51,667 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 16:03:51,668 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 16:03:51,672 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 16:03:51,674 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 16:03:51,688 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 16:03:51,697 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 16:03:51,714 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 16:03:51,718 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 16:03:51,719 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 18:01:31,670 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 18:01:31,679 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 18:01:31,681 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 18:01:31,683 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 18:01:31,685 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 18:01:31,703 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 18:01:31,706 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 18:01:31,707 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 18:01:31,712 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 18:01:31,715 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 18:01:31,725 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 18:01:31,732 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 18:01:31,746 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 18:02:31,773 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-13 18:02:31,777 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-13 18:02:31,778 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-13 18:02:31,786 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-13 18:02:31,798 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-13 18:02:31,803 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-13 18:02:31,812 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-13 18:02:31,826 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-13 18:02:31,834 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-13 18:02:31,844 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-13 18:02:31,846 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-13 18:02:31,849 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-13 18:02:31,861 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-13 18:05:33,539 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-13 18:05:33,544 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-13 18:05:33,617 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-13 18:05:33,618 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 15:56:49,166 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 15:56:49,169 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for site1
2025-06-15 15:56:49,172 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for site1
2025-06-15 15:56:49,175 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for site1
2025-06-15 15:56:49,178 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 15:56:49,181 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for site1
2025-06-15 15:56:49,183 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 15:56:49,185 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for site1
2025-06-15 15:56:49,187 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for site1
2025-06-15 15:56:49,189 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 15:56:49,190 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for site1
2025-06-15 15:56:49,192 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for site1
2025-06-15 15:56:49,193 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for site1
2025-06-15 15:56:49,194 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for site1
2025-06-15 15:56:49,195 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 15:56:49,196 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for site1
2025-06-15 15:56:49,197 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for site1
2025-06-15 15:56:49,199 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 15:56:49,200 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for site1
2025-06-15 15:56:49,201 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for site1
2025-06-15 15:56:49,202 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for site1
2025-06-15 15:56:49,203 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 15:56:49,204 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 15:56:49,205 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for site1
2025-06-15 15:56:49,205 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 15:56:49,206 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for site1
2025-06-15 15:56:49,207 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 15:56:49,208 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 15:56:49,209 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for site1
2025-06-15 15:56:49,210 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for site1
2025-06-15 15:56:49,210 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for site1
2025-06-15 15:56:49,211 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for site1
2025-06-15 15:56:49,212 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 15:56:49,213 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for site1
2025-06-15 15:56:49,214 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 15:56:49,215 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for site1
2025-06-15 15:56:49,216 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 15:56:49,217 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 15:56:49,218 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for site1
2025-06-15 15:56:49,219 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 15:56:49,220 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 15:56:49,220 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for site1
2025-06-15 15:56:49,221 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for site1
2025-06-15 15:56:49,223 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for site1
2025-06-15 15:56:49,223 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 15:56:49,225 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 15:56:49,226 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 15:56:49,227 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for site1
2025-06-15 15:56:49,227 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 15:56:49,228 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for site1
2025-06-15 15:56:49,229 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 15:56:49,230 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 15:56:49,231 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 15:56:49,231 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 15:56:49,232 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 15:56:49,233 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for site1
2025-06-15 15:56:49,234 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 15:56:49,236 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 15:56:49,236 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for site1
2025-06-15 15:56:49,237 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for site1
2025-06-15 15:56:49,238 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for site1
2025-06-15 15:56:49,239 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for site1
2025-06-15 15:56:49,240 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 15:56:49,240 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 15:56:49,241 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 15:56:49,242 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for site1
2025-06-15 15:56:49,243 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 15:56:49,244 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 15:56:49,245 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for site1
2025-06-15 15:56:49,246 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for site1
2025-06-15 15:56:49,247 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for site1
2025-06-15 15:56:49,248 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 15:56:49,249 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for site1
2025-06-15 15:56:49,250 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 15:56:49,251 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for site1
2025-06-15 15:56:49,252 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for site1
2025-06-15 15:56:49,253 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for site1
2025-06-15 15:56:49,254 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for site1
2025-06-15 15:56:49,254 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for site1
2025-06-15 15:56:49,255 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
2025-06-15 15:56:49,256 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 15:56:49,257 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for site1
2025-06-15 15:56:49,258 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for site1
2025-06-15 15:56:49,259 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 15:56:49,259 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for site1
2025-06-15 15:56:49,260 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 15:56:49,261 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for site1
2025-06-15 15:56:49,262 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for site1
2025-06-15 15:56:49,263 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for site1
2025-06-15 15:56:49,264 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 15:56:49,264 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for site1
2025-06-15 15:56:49,265 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for site1
2025-06-15 15:56:49,266 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 15:56:49,267 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 15:56:49,268 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for site1
2025-06-15 15:56:49,269 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for site1
2025-06-15 15:56:49,270 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 15:56:49,271 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 15:56:49,272 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 15:56:49,273 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 15:56:49,273 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 15:56:49,274 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for site1
2025-06-15 15:56:49,275 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 15:56:49,276 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for site1
2025-06-15 15:56:49,277 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 15:56:49,278 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 15:56:49,278 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for site1
2025-06-15 15:56:49,279 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 15:57:49,760 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for site1
2025-06-15 15:57:49,761 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for site1
2025-06-15 15:57:49,762 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for site1
2025-06-15 15:57:49,763 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for site1
2025-06-15 15:57:49,764 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for site1
2025-06-15 15:57:49,765 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for site1
2025-06-15 15:57:49,766 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for site1
2025-06-15 15:57:49,767 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for site1
2025-06-15 15:57:49,768 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for site1
2025-06-15 15:57:49,769 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for site1
2025-06-15 15:57:49,770 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for site1
2025-06-15 15:57:49,770 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for site1
2025-06-15 15:57:49,771 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for site1
2025-06-15 15:57:49,772 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 15:57:49,773 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for site1
2025-06-15 15:57:49,774 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for site1
2025-06-15 15:57:49,775 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for site1
2025-06-15 15:57:49,776 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for site1
2025-06-15 15:57:49,776 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for site1
2025-06-15 15:57:49,778 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for site1
2025-06-15 15:57:49,779 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for site1
2025-06-15 15:57:49,779 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for site1
2025-06-15 15:57:49,780 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for site1
2025-06-15 15:57:49,781 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for site1
2025-06-15 15:57:49,782 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for site1
2025-06-15 15:57:49,783 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for site1
2025-06-15 15:57:49,784 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for site1
2025-06-15 15:57:49,785 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for site1
2025-06-15 15:57:49,786 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for site1
2025-06-15 15:57:49,787 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for site1
2025-06-15 15:57:49,788 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for site1
2025-06-15 15:57:49,789 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for site1
2025-06-15 15:57:49,790 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for site1
2025-06-15 15:57:49,791 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for site1
2025-06-15 15:57:49,792 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for site1
2025-06-15 15:57:49,793 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for site1
2025-06-15 15:57:49,794 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for site1
2025-06-15 15:57:49,796 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for site1
2025-06-15 15:57:49,797 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for site1
2025-06-15 15:57:49,798 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for site1
2025-06-15 15:57:49,799 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for site1
2025-06-15 15:57:49,800 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for site1
2025-06-15 15:57:49,801 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for site1
2025-06-15 15:57:49,803 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for site1
2025-06-15 15:57:49,804 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for site1
2025-06-15 15:57:49,806 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for site1
2025-06-15 15:57:49,807 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for site1
2025-06-15 15:57:49,808 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for site1
2025-06-15 15:57:49,809 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for site1
2025-06-15 15:57:49,809 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for site1
2025-06-15 15:57:49,810 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for site1
2025-06-15 15:57:49,811 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for site1
2025-06-15 15:57:49,812 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for site1
2025-06-15 15:57:49,813 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for site1
2025-06-15 15:57:49,813 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for site1
2025-06-15 15:57:49,814 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for site1
2025-06-15 15:57:49,815 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for site1
2025-06-15 15:57:49,816 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for site1
2025-06-15 15:57:49,817 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for site1
2025-06-15 15:57:49,818 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for site1
2025-06-15 15:57:49,819 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for site1
2025-06-15 15:57:49,820 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for site1
2025-06-15 15:57:49,821 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for site1
2025-06-15 15:57:49,822 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for site1
2025-06-15 15:57:49,822 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for site1
2025-06-15 15:57:49,823 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for site1
2025-06-15 15:57:49,824 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for site1
2025-06-15 15:57:49,825 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for site1
2025-06-15 15:57:49,826 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for site1
2025-06-15 15:57:49,827 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for site1
2025-06-15 15:57:49,828 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for site1
2025-06-15 15:57:49,829 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for site1
2025-06-15 15:57:49,830 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for site1
2025-06-15 15:57:49,830 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for site1
2025-06-15 15:57:49,831 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for site1
2025-06-15 15:57:49,832 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for site1
2025-06-15 15:57:49,833 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for site1
2025-06-15 15:57:49,834 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for site1
2025-06-15 15:57:49,835 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for site1
2025-06-15 15:57:49,836 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for site1
2025-06-15 15:57:49,837 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for site1
2025-06-15 15:57:49,838 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for site1
2025-06-15 15:57:49,839 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for site1
2025-06-15 15:57:49,840 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for site1
2025-06-15 15:57:49,840 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for site1
