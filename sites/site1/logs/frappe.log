2025-06-04 16:33:36,193 ERROR frappe Failed to capture exception
Site: site1
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-05 11:26:30,080 ERROR frappe Failed to capture exception
Site: site1
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-13 14:28:57,767 ERROR frappe Failed to rename IPROTECT SECURITY: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,771 ERROR frappe Failed to rename fortis insurance: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,775 ERROR frappe Failed to rename FOXTREKS LTD: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,777 ERROR frappe Failed to rename TAHA STORE LIMITED: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,780 ERROR frappe Failed to rename MWINYI ALLY RAMADHANI: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,783 ERROR frappe Failed to rename sumpra ltd: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,785 ERROR frappe Failed to rename darsh industrial ltd: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,788 ERROR frappe Failed to rename DAMA GROUP LIMITED: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,790 ERROR frappe Failed to rename LUGEYE: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,792 ERROR frappe Failed to rename Y & p architects (t) ltd: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,795 ERROR frappe Failed to rename brac tanzania finance ltd: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,797 ERROR frappe Failed to rename Masanja Wilson Emmanuel: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,800 ERROR frappe Failed to rename China railway major bridge engineering group co ltd: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 14:28:57,803 ERROR frappe Failed to rename CASH: rename_doc() got an unexpected keyword argument 'ignore_permissions'
Site: site1
Form Dict: {}
2025-06-13 16:37:55,123 ERROR frappe New Exception collected in error log
Site: site1
Form Dict: {'doctype': 'My website', 'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-06-19 10:16:17,654 ERROR frappe New Exception collected in error log
Site: site1
Form Dict: {'item': 'Camera', 'cmd': 'propms.issue_hook.get_item_rate'}
