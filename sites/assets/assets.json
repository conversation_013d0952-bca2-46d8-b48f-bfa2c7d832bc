{"billing.bundle.js": "/assets/frappe/dist/js/billing.bundle.3M2NKQ7F.js", "bootstrap-4-web.bundle.js": "/assets/frappe/dist/js/bootstrap-4-web.bundle.AZ67VXZX.js", "controls.bundle.js": "/assets/frappe/dist/js/controls.bundle.LKLYGEEA.js", "data_import_tools.bundle.js": "/assets/frappe/dist/js/data_import_tools.bundle.F7I46Y5V.js", "desk.bundle.js": "/assets/frappe/dist/js/desk.bundle.RIGSWZKY.js", "dialog.bundle.js": "/assets/frappe/dist/js/dialog.bundle.5T3VDFB7.js", "form.bundle.js": "/assets/frappe/dist/js/form.bundle.ALYJOYOH.js", "frappe-web.bundle.js": "/assets/frappe/dist/js/frappe-web.bundle.HMSS3FDS.js", "libs.bundle.js": "/assets/frappe/dist/js/libs.bundle.LLRFRX7M.js", "list.bundle.js": "/assets/frappe/dist/js/list.bundle.GSICJLZI.js", "logtypes.bundle.js": "/assets/frappe/dist/js/logtypes.bundle.MJKW7EK3.js", "onboarding_tours.bundle.js": "/assets/frappe/dist/js/onboarding_tours.bundle.P7QYMXLW.js", "report.bundle.js": "/assets/frappe/dist/js/report.bundle.GJVO743T.js", "sentry.bundle.js": "/assets/frappe/dist/js/sentry.bundle.SI3DB3BY.js", "telemetry.bundle.js": "/assets/frappe/dist/js/telemetry.bundle.ZJBT5ETW.js", "user_profile_controller.bundle.js": "/assets/frappe/dist/js/user_profile_controller.bundle.TAMQL3L3.js", "video_player.bundle.js": "/assets/frappe/dist/js/video_player.bundle.IOEIXC2G.js", "web_form.bundle.js": "/assets/frappe/dist/js/web_form.bundle.XAYNSBNB.js", "form_builder.bundle.js": "/assets/frappe/dist/js/form_builder.bundle.T33CMAQH.js", "print_format_builder.bundle.js": "/assets/frappe/dist/js/print_format_builder.bundle.7BZNJJCV.js", "workflow_builder.bundle.js": "/assets/frappe/dist/js/workflow_builder.bundle.ZYV47TEV.js", "build_events.bundle.js": "/assets/frappe/dist/js/build_events.bundle.L4QLVDWF.js", "file_uploader.bundle.js": "/assets/frappe/dist/js/file_uploader.bundle.L5FH4LH7.js", "kanban_board.bundle.js": "/assets/frappe/dist/js/kanban_board.bundle.OUFA2R27.js", "desk.bundle.css": "/assets/frappe/dist/css/desk.bundle.MQ2SH4GF.css", "email.bundle.css": "/assets/frappe/dist/css/email.bundle.UT7WDYKV.css", "login.bundle.css": "/assets/frappe/dist/css/login.bundle.5NAESDFD.css", "print.bundle.css": "/assets/frappe/dist/css/print.bundle.O7SS3I35.css", "print_format.bundle.css": "/assets/frappe/dist/css/print_format.bundle.GWA2QPGF.css", "report.bundle.css": "/assets/frappe/dist/css/report.bundle.ETHXZ52D.css", "web_form.bundle.css": "/assets/frappe/dist/css/web_form.bundle.FYIDXPTC.css", "website.bundle.css": "/assets/frappe/dist/css/website.bundle.MJ6CL6NO.css", "bank-reconciliation-tool.bundle.js": "/assets/erpnext/dist/js/bank-reconciliation-tool.bundle.G7MHMAFO.js", "erpnext-web.bundle.js": "/assets/erpnext/dist/js/erpnext-web.bundle.253I7LT4.js", "erpnext.bundle.js": "/assets/erpnext/dist/js/erpnext.bundle.OTY4HTA4.js", "item-dashboard.bundle.js": "/assets/erpnext/dist/js/item-dashboard.bundle.NIG5UGYE.js", "point-of-sale.bundle.js": "/assets/erpnext/dist/js/point-of-sale.bundle.SWII2FP3.js", "bom_configurator.bundle.js": "/assets/erpnext/dist/js/bom_configurator.bundle.PLBYQFQX.js", "erpnext-web.bundle.css": "/assets/erpnext/dist/css/erpnext-web.bundle.APJNLTFT.css", "erpnext.bundle.css": "/assets/erpnext/dist/css/erpnext.bundle.AYIBSUK7.css", "erpnext_email.bundle.css": "/assets/erpnext/dist/css/erpnext_email.bundle.UR3PQEUG.css", "hierarchy-chart.bundle.js": "/assets/hrms/dist/js/hierarchy-chart.bundle.ISWSVX4F.js", "hrms.bundle.js": "/assets/hrms/dist/js/hrms.bundle.KJGFTGNV.js", "interview.bundle.js": "/assets/hrms/dist/js/interview.bundle.IQ53CD67.js", "performance.bundle.js": "/assets/hrms/dist/js/performance.bundle.LREZFD62.js", "hrms.bundle.css": "/assets/hrms/dist/css/hrms.bundle.IFB2AMU6.css", "healthcare.bundle.js": "/assets/healthcare/dist/js/healthcare.bundle.VGZYK3JM.js", "csf_tz.bundle.js": "/assets/csf_tz/dist/js/csf_tz.bundle.IPPKUKBU.js", "hms_tz.bundle.js": "/assets/hms_tz/dist/js/hms_tz.bundle.HIK5ZYJZ.js"}