2025-06-12 18:30:18,255 WARNING database DDL Query made to DB:
create table `tabRepack Template Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_target_warehouse` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_uom` varchar(140),
`qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,317 WARNING database DDL Query made to DB:
create table `tabElectronic Fiscal Device` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`serial_no` varchar(140),
`location` varchar(140),
`supplier` varchar(140),
`make` varchar(140),
`model` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,428 WARNING database DDL Query made to DB:
create table `tabAttachment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,505 WARNING database DDL Query made to DB:
create table `tabPrice Change Request Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`price_list` varchar(140),
`old_price` decimal(21,9) not null default 0,
`item_name` varchar(140),
`cost` decimal(21,9) not null default 0,
`new_price` decimal(21,9) not null default 0,
`valid_from` date,
`valid_to` date,
`price_list_currency` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,570 WARNING database DDL Query made to DB:
create table `tabCSF TZ Bank Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`bank_account` varchar(140),
`currency` varchar(140),
`company` varchar(140),
`account` varchar(140),
`bank_supplier` varchar(140),
`exchange_rate` decimal(21,9) not null default 1.0,
`total_bank_charges` decimal(21,9) not null default 0,
`ref_pi` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,642 WARNING database DDL Query made to DB:
create table `tabInter Company Stock Transfer Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`item_code` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`batch_no` varchar(140),
`bom_no` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`s_warehouse` varchar(140),
`t_warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,740 WARNING database DDL Query made to DB:
create table `tabBackground Document Posting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`posting_type` varchar(140),
`timeout` int(11) not null default 600,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,812 WARNING database DDL Query made to DB:
create table `tabSingle Piecework Employees` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`task` varchar(140),
`task_name` varchar(140),
`task_rate` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,867 WARNING database DDL Query made to DB:
create table `tabVehicle Consignment Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`bom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:18,927 WARNING database DDL Query made to DB:
create table `tabCSF TZ Bank Charges Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`value_date` date,
`control_number` varchar(140),
`description` text,
`reference_number` varchar(140),
`debit_amount` decimal(21,9) not null default 0,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,000 WARNING database DDL Query made to DB:
create table `tabNMB Callback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`timestamp` datetime(6),
`reference` varchar(140),
`receipt` varchar(140),
`amount` decimal(21,9) not null default 0,
`customer_name` varchar(140),
`account_number` varchar(140),
`token` varchar(140),
`fees_token` varchar(140),
`channel` varchar(140),
`payment_entry` varchar(140),
`api_key` varchar(140),
`api_secret` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,126 WARNING database DDL Query made to DB:
create table `tabReporting Currency Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable_reporting_currency` int(1) not null default 0,
`reporting_currency` varchar(140),
`company` varchar(140) unique,
`recalculation_start_date` date,
`recalculation_end_date` date,
`recalculation_schedule_frequency` varchar(140),
`recalculation_schedule_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,192 WARNING database DDL Query made to DB:
create table `tabScheduled Auto Email Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`auto_email_report` varchar(140) unique,
`enabled` int(1) not null default 0,
`schedule` varchar(140),
`day_of_month` int(11) not null default 0,
`schedule_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,341 WARNING database DDL Query made to DB:
create table `tabBank Charges Pattern` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`bank_account` varchar(140),
`bank_charges_pattern` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,422 WARNING database DDL Query made to DB:
create table `tabEFD Z Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`electronic_fiscal_device` varchar(140),
`z_no` varchar(140),
`money` decimal(21,9) not null default 0,
`z_report_date_time` datetime(6),
`receipts_issued` int(11) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`total_vat` decimal(21,9) not null default 0,
`total_turnover_ex_sr` decimal(21,9) not null default 0,
`total_turnover` decimal(21,9) not null default 0,
`allowable_difference` decimal(21,9) not null default 0,
`total_excluding_vat_ticked` decimal(21,9) not null default 0,
`total_vat_ticked` decimal(21,9) not null default 0,
`total_turnover_exempted__sp_relief_ticked` decimal(21,9) not null default 0,
`total_turnover_ticked` decimal(21,9) not null default 0,
`a_turnover` decimal(21,9) not null default 0,
`b_turnover` decimal(21,9) not null default 0,
`c_turnover` decimal(21,9) not null default 0,
`d_turnover` decimal(21,9) not null default 0,
`e_turnover` decimal(21,9) not null default 0,
`a_net_sum` decimal(21,9) not null default 0,
`b_net_sum` decimal(21,9) not null default 0,
`c_net_sum` decimal(21,9) not null default 0,
`d_net_sum` decimal(21,9) not null default 0,
`a_vat` decimal(21,9) not null default 0,
`b_vat` decimal(21,9) not null default 0,
`c_vat` decimal(21,9) not null default 0,
`d_vat` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,484 WARNING database DDL Query made to DB:
create table `tabPiecework Single` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,553 WARNING database DDL Query made to DB:
create table `tabPiecework` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`company` varchar(140),
`task` varchar(140),
`quantity` decimal(21,9) not null default 0,
`task_name` varchar(140),
`task_rate` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,630 WARNING database DDL Query made to DB:
create table `tabTZ Insurance Policy Holder Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`createddate` varchar(140),
`updateddate` varchar(140),
`createdby` varchar(140),
`updatedby` varchar(140),
`id` varchar(140),
`policyholdertypeid` varchar(140),
`policyholderfullname` varchar(140),
`policyholderbirthdate` varchar(140),
`policyholderidentitynumber` varchar(140),
`policyholderidentitytypeid` varchar(140),
`genderid` varchar(140),
`countryid` varchar(140),
`countrycode` varchar(140),
`districtid` varchar(140),
`districtname` varchar(140),
`regionname` varchar(140),
`locationstreet` varchar(140),
`policyholderphone1` varchar(140),
`policyholderphone2` varchar(140),
`policyholderphone3` varchar(140),
`policyholderfax` varchar(140),
`postaladdress` varchar(140),
`emailaddress` varchar(140),
`companyid` varchar(140),
`statusid` varchar(140),
`systemid` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,694 WARNING database DDL Query made to DB:
create table `tabVehicle Fine Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference` varchar(140) unique,
`issued_date` varchar(140),
`officer` varchar(140),
`vehicle` varchar(140),
`vehicle_doc` varchar(140),
`licence` varchar(140),
`status` varchar(140),
`offence` text,
`charge` decimal(21,9) not null default 0,
`penalty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`location` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,758 WARNING database DDL Query made to DB:
create table `tabEmail Salary Slips` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,837 WARNING database DDL Query made to DB:
create table `tabParking Bill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`vehicle` varchar(140),
`billstatus` varchar(140),
`billid` varchar(140),
`approvedby` varchar(140),
`billdescription` varchar(140),
`billpayed` int(1) not null default 0,
`billreference` varchar(140) unique,
`billedamount` decimal(21,9) not null default 0,
`billcontrolnumber` varchar(140),
`billequivalentamount` decimal(21,9) not null default 0,
`expirydate` date,
`generateddate` date,
`miscellaneousamount` decimal(21,9) not null default 0,
`payeremail` varchar(140),
`remarks` varchar(140),
`payerphone` varchar(140),
`payername` varchar(140),
`reminderflag` varchar(140),
`spsystemid` varchar(140),
`billpaytype` varchar(140),
`receivedtime` varchar(140),
`billcurrency` varchar(140),
`applicationid` varchar(140),
`collectioncode` varchar(140),
`type` varchar(140),
`createdby` varchar(140),
`itemid` varchar(140),
`parkingdetailsid` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,904 WARNING database DDL Query made to DB:
create table `tabDelivery Exchange Item Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_sold_or_delivered` varchar(140),
`rate_sold_or_delivered` varchar(140),
`qty_sold_or_delivered` varchar(140),
`amount_sold_or_delivered` varchar(140),
`warehouse` varchar(140),
`item_exchange` varchar(140),
`amount_exchange` varchar(140),
`uom` varchar(140),
`amended_from` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:19,982 WARNING database DDL Query made to DB:
create table `tabInter Company Stock Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`from_company` varchar(140),
`default_from_warehouse` varchar(140),
`to_company` varchar(140),
`default_to_warehouse` varchar(140),
`material_receipt` varchar(140),
`inter_company_material_request` varchar(140),
`material_issue` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,047 WARNING database DDL Query made to DB:
create table `tabBank Clearance Pro Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_document` varchar(140),
`payment_entry` varchar(140),
`against_account` varchar(140),
`amount` varchar(140),
`flt_amount` decimal(21,9) not null default 0,
`posting_date` date,
`cheque_number` varchar(140),
`cheque_date` date,
`clearance_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,099 WARNING database DDL Query made to DB:
create table `tabDynamic Price List Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`warehouse` varchar(140),
`supplier` varchar(140),
`price_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,156 WARNING database DDL Query made to DB:
create table `tabStation Members` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,222 WARNING database DDL Query made to DB:
create table `tabExpense Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`date` date,
`section` varchar(140),
`purchase_invoice` varchar(140),
`supplier` varchar(140),
`expense_type` varchar(140),
`item` varchar(140),
`bill_no` varchar(140),
`amount` decimal(21,9) not null default 0,
`attach_receipt` text,
`journal_entry` varchar(140),
`section_manager` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,315 WARNING database DDL Query made to DB:
create table `tabInter Company Material Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`from_company` varchar(140),
`default_from_warehouse` varchar(140),
`to_company` varchar(140),
`default_to_warehouse` varchar(140),
`inter_company_stock_transfer` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,374 WARNING database DDL Query made to DB:
create table `tabEmail Employee Salary Slip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`send_email` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,446 WARNING database DDL Query made to DB:
create table `tabSection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`section_name` varchar(140) unique,
`company` varchar(140),
`section_full_name` varchar(140),
`section_manager` varchar(140),
`business_license_due_date` date,
`workplace_license_expiry` date,
`cost_center` varchar(140),
`stock_adjustment` varchar(140),
`purchase_taxes_and_charges_template` varchar(140),
`default_cash_account` varchar(140),
`default_warehouse` varchar(140),
`cash_customer` varchar(140),
`monthly_target` decimal(21,9) not null default 0,
`cash_customer_pos_profile` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,521 WARNING database DDL Query made to DB:
create table `tabOpen Invoice Exchange Rate Revaluation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`revaluation_date` date,
`currency` varchar(140) default 'USD',
`exchange_rate_to_company_currency` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`journal_entry` varchar(140),
`reverse_journal_entry` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,586 WARNING database DDL Query made to DB:
create table `tabSpecial Closing Balance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`warehouse` varchar(140),
`shift` varchar(140),
`stock_entry` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,650 WARNING database DDL Query made to DB:
create table `tabInv ERR Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invoice_type` varchar(140),
`invoice_number` varchar(140),
`invoice_currency` varchar(140),
`invoice_amount` decimal(21,9) not null default 0,
`invoice_exchange_rate` decimal(21,9) not null default 0,
`invoice_gain_or_loss` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,709 WARNING database DDL Query made to DB:
create table `tabEmployee Salary Component Limit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`valid_from` date,
`limit_amount` decimal(21,9) not null default 0,
`limit_number` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,783 WARNING database DDL Query made to DB:
create table `tabTZ Ward` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ward` varchar(140) unique,
`district` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,849 WARNING database DDL Query made to DB:
create table `tabTZ Region` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`region` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,916 WARNING database DDL Query made to DB:
create table `tabRepack Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`item_uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`default_warehouse` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:20,977 WARNING database DDL Query made to DB:
create table `tabParking Bill Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`id` varchar(140),
`collectorid` varchar(140),
`councilcode` varchar(140),
`intime` datetime(6),
`outtime` datetime(6),
`detailinsertionstatus` varchar(140),
`coordinates` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,039 WARNING database DDL Query made to DB:
create table `tabPayment Reconciliation Pro Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`posting_date` date,
`is_advance` varchar(140),
`reference_row` varchar(140),
`invoice_number` varchar(140),
`amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`difference_account` varchar(140),
`difference_amount` decimal(21,9) not null default 0,
`remark` text,
`currency` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,103 WARNING database DDL Query made to DB:
create table `tabSpecial Closing Balance Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`uom` varchar(140),
`quantity` decimal(21,9) not null default 0,
`item_balance` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,158 WARNING database DDL Query made to DB:
create table `tabEmployee Piecework Additional Salary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amount` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,213 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`quantity` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,327 WARNING database DDL Query made to DB:
create table `tabTZ Insurance Vehicle Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`registrationnumber` varchar(140),
`make` varchar(140),
`model` varchar(140),
`enginenumber` varchar(140),
`enginecapacity` varchar(140),
`numberofaxles` varchar(140),
`sittingcapacity` varchar(140),
`createddate` varchar(140),
`createdby` varchar(140),
`chassisnumber` varchar(140),
`bodytype` varchar(140),
`modelnumber` varchar(140),
`color` varchar(140),
`fuelused` varchar(140),
`axledistance` varchar(140),
`id` varchar(140),
`updateddate` varchar(140),
`updatedby` varchar(140),
`tareweight` varchar(140),
`grossweight` varchar(140),
`motorusageid` varchar(140),
`ownername` varchar(140),
`owneraddress` varchar(140),
`ownercategoryid` varchar(140),
`motorcategoryid` varchar(140),
`covernoteid` varchar(140),
`yearofmanufacture` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,403 WARNING database DDL Query made to DB:
create table `tabPossible Root Cause` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`root_cause_of_the_issue` varchar(140),
`rate_probability` varchar(140),
`description` text,
`categorize_root_cause` varchar(140),
`please_specify` text,
`investigation` varchar(140),
`investigation_area` varchar(140),
`interview_conducted` varchar(140),
`interview_summary` text,
`number_of_people_interviewed` varchar(140),
`name_of_interviewer` text,
`findings_of_investigation` text,
`evidence_available` varchar(140),
`list_documented_evidence` text,
`attach_evidence` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,468 WARNING database DDL Query made to DB:
create table `tabMaintenance Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`maintenance_request` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,527 WARNING database DDL Query made to DB:
create table `tabReporting Currency Settings Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`rate_doctype` varchar(140),
`rate_docfield` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,585 WARNING database DDL Query made to DB:
create table `tabWork Order Consignment Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`bom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,656 WARNING database DDL Query made to DB:
create table `tabParking Bill Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`billitemrefid` varchar(140),
`billitemref` varchar(140),
`billitemamount` decimal(21,9) not null default 0,
`billitemmiscamount` decimal(21,9) not null default 0,
`billitemeqvamount` decimal(21,9) not null default 0,
`billitemdescription` varchar(140),
`date` date,
`sourcename` varchar(140),
`gsfcode` varchar(140),
`parkingdetailsid` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,711 WARNING database DDL Query made to DB:
create table `tabOTP Register` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`party_type` varchar(140),
`party` varchar(140),
`party_name` varchar(140),
`user_name` varchar(140),
`otp_type` varchar(140),
`validated` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,801 WARNING database DDL Query made to DB:
create table `tabVisibility` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`subject` varchar(140),
`document_type` varchar(140),
`is_standard` int(1) not null default 0,
`module` varchar(140),
`event` varchar(140),
`method` varchar(140),
`date_changed` varchar(140),
`days_in_advance` int(11) not null default 0,
`value_changed` varchar(140),
`set_property_after_alert` varchar(140),
`property_value` varchar(140),
`condition` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `document_type`(`document_type`),
index `event`(`event`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,884 WARNING database DDL Query made to DB:
create table `tabPayment Reconciliation Pro Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invoice_type` varchar(140),
`invoice_number` varchar(140),
`invoice_date` date,
`amount` decimal(21,9) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`currency` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:21,938 WARNING database DDL Query made to DB:
create table `tabFile Attachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`file_attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,005 WARNING database DDL Query made to DB:
create table `tabVehicle Location Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type_of_update` varchar(140) default 'Manual Update',
`vehicle` varchar(140),
`location` varchar(140),
`location_details` varchar(140),
`longitude` varchar(140),
`latitude` varchar(140),
`comment` text,
`timestamp` datetime(6),
`map` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,072 WARNING database DDL Query made to DB:
create table `tabRoot Cause Prevention Strategy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`solution_to_be_implemented` varchar(1000),
`consideration` varchar(140),
`specify_considerations` text,
`is_solution_implemented` varchar(140),
`estimated_cost` decimal(21,9) not null default 0,
`incidental_findings` varchar(140),
`specify_findings` text,
`date_of_completion` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,157 WARNING database DDL Query made to DB:
create table `tabStudent Applicant Fees` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`fee_schedule` varchar(140),
`include_payment` int(1) not null default 0,
`send_payment_request` int(1) not null default 0,
`bank_reference` varchar(140),
`company` varchar(140),
`abbr` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`due_date` date,
`program_enrollment` varchar(140),
`program` varchar(140),
`student_batch` varchar(140),
`student_email` varchar(140),
`student_category` varchar(140),
`academic_term` varchar(140),
`academic_year` varchar(140),
`currency` varchar(140),
`fee_structure` varchar(140),
`grand_total` decimal(21,9) not null default 0,
`grand_total_in_words` varchar(140),
`outstanding_amount` decimal(21,9) not null default 0,
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`receivable_account` varchar(140),
`income_account` varchar(140),
`callback_token` varchar(140),
`cost_center` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_date`(`posting_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,246 WARNING database DDL Query made to DB:
create table `tabCSF API Response Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`timestamp` datetime(6),
`request_type` varchar(140),
`request_url` varchar(140),
`request_header` text,
`user_id` varchar(140),
`status_code` varchar(140),
`request_body` longtext,
`response_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,306 WARNING database DDL Query made to DB:
create table `tabWork Order Consignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'WOCO-.YYYY.-.######',
`parent_item` varchar(140),
`company` varchar(140),
`quantity` int(11) not null default 0,
`default_source_warehouse` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,373 WARNING database DDL Query made to DB:
create table `tabSQL Process Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`id` varchar(140),
`time` int(11) not null default 0,
`query` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,437 WARNING database DDL Query made to DB:
create table `tabPrice Change Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`posting_date` date,
`requested_by` varchar(140),
`approved_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,515 WARNING database DDL Query made to DB:
create table `tabRoot Cause Analysis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`date_issue_happened` date,
`department_issue_identified` varchar(140),
`impact_to_productivity` varchar(140),
`is_recurring_issue` varchar(140),
`frequency_of_the_issue` varchar(140),
`department_affected` text,
`description_of_the_issue` text,
`reported_by` varchar(140),
`phone_no` varchar(140),
`status` varchar(140),
`reported_date` date,
`solved_by` text,
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,575 WARNING database DDL Query made to DB:
create table `tabEFD Z Report Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invoice_number` varchar(140),
`invoice_date` date,
`amt_excl_vat` decimal(21,9) not null default 0,
`vat` decimal(21,9) not null default 0,
`amt_ex__sr` decimal(21,9) not null default 0,
`invoice_amount` decimal(21,9) not null default 0,
`include` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,634 WARNING database DDL Query made to DB:
create table `tabDelivery Exchange Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`remarks` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,697 WARNING database DDL Query made to DB:
create table `tabVehicle Consignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent_item` varchar(140),
`quantity` int(11) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,756 WARNING database DDL Query made to DB:
create table `tabSQL Command` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`doctype_name` varchar(140),
`names` text,
`sql_text` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,898 WARNING database DDL Query made to DB:
create table `tabPiecework Salary Disbursement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_date` date,
`end_date` date,
`payroll_date` date,
`earning_salary_component` varchar(140),
`deduction_salary_component` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:22,956 WARNING database DDL Query made to DB:
create table `tabDelivery Exchange Non Stock Item Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_sold_or_delivered` varchar(140),
`rate_sold_or_delivered` varchar(140),
`qty_sold_or_delivered` varchar(140),
`amount_sold_or_delivered` varchar(140),
`warehouse` varchar(140),
`item_exchange` varchar(140),
`amount_exchange` varchar(140),
`uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,016 WARNING database DDL Query made to DB:
create table `tabBOM Additional Costs` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cost_type` varchar(140),
`cost_per_unit` decimal(21,9) not null default 0,
`expense_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,076 WARNING database DDL Query made to DB:
create table `tabTZ Village` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`village` varchar(140) unique,
`ward` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,153 WARNING database DDL Query made to DB:
create table `tabExpense Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_type` varchar(140),
`section` varchar(140),
`company` varchar(140),
`expense_account` varchar(140),
`item` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,220 WARNING database DDL Query made to DB:
create table `tabDocument Attachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_name` varchar(140),
`document_number` varchar(140),
`issue_date` date,
`expiry_date` date,
`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,276 WARNING database DDL Query made to DB:
create table `tabPiecework Payment Allocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amount` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,340 WARNING database DDL Query made to DB:
create table `tabInter Company Material Request Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`item_code` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`batch_no` varchar(140),
`bom_no` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`s_warehouse` varchar(140),
`t_warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,413 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type` varchar(140) default 'Sales',
`verification_code` varchar(140) unique,
`verification_status` varchar(140) default 'Pending',
`verification_date` date,
`company_name` varchar(140),
`receipt_number` varchar(140),
`subtotal` decimal(21,9) not null default 0,
`total_tax` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,481 WARNING database DDL Query made to DB:
create table `tabEmployee OT Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`no_of_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,604 WARNING database DDL Query made to DB:
create table `tabTZ Insurance Company Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`createddate` varchar(140),
`updateddate` varchar(140),
`createdby` varchar(140),
`updatedby` varchar(140),
`id` varchar(140),
`companycode` varchar(140),
`companynumber` varchar(140),
`companytypeid` varchar(140),
`companyname` varchar(140),
`businesstypeid` varchar(140),
`incorporationcertificatenumber` varchar(140),
`numberlocalstaff` varchar(140),
`numberforeignstaff` varchar(140),
`totalshareowned` varchar(140),
`totalsharealloted` varchar(140),
`incorporationdate` varchar(140),
`initialregistrationdate` varchar(140),
`businesscommencementdate` varchar(140),
`countryid` varchar(140),
`regionid` varchar(140),
`districtid` varchar(140),
`locationstreet` varchar(140),
`companyphone1` varchar(140),
`companyphone2` varchar(140),
`companyphone3` varchar(140),
`companyfax` varchar(140),
`postaladdress` varchar(140),
`emailaddress` varchar(140),
`statusid` varchar(140),
`smsnotification` varchar(140),
`emailnotification` varchar(140),
`shareholders` varchar(1000),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:23,674 WARNING database DDL Query made to DB:
create table `tabReporting GL Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gl_entry` varchar(140),
`posting_date` date,
`reporting_currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`debit_amount` decimal(21,9) not null default 0,
`credit_amount` decimal(21,9) not null default 0,
`day_exchange_rate` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,353 WARNING database DDL Query made to DB:
create table `tabReference Payment Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`accounting_system` varchar(140) default 'Pastel',
`amount` decimal(21,9) not null default 0,
`currency` varchar(140),
`date_of_payment` date,
`reference_no` varchar(140),
`paid_to` varchar(140),
`payment_method` varchar(140),
`payment_account` varchar(140),
`status` varchar(140) default 'new',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,413 WARNING database DDL Query made to DB:
create table `tabPre Delivery Inspection Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`serial_no` varchar(140),
`checked_parameter` varchar(140),
`sales_invoice` varchar(140),
`sales_order` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,480 WARNING database DDL Query made to DB:
create table `tabMachine Strip Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`item_name` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,560 WARNING database DDL Query made to DB:
create table `tabPre Delivery Inspection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`report_date` date,
`customer` varchar(140),
`inspection_type` varchar(140),
`reference_type` varchar(140),
`reference_name` varchar(140),
`item_code` varchar(140),
`item_serial_no` varchar(140),
`batch_no` varchar(140),
`sample_size` decimal(21,9) not null default 0,
`item_name` varchar(140),
`description` text,
`quality_inspection_template` varchar(140),
`inspected_by` varchar(140) default 'user',
`verified_by` varchar(140),
`bom_no` varchar(140),
`remarks` text,
`amended_from` varchar(140),
`sales_invoice` varchar(140),
`sales_order` varchar(140),
`installation_note` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `report_date`(`report_date`),
index `item_code`(`item_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,630 WARNING database DDL Query made to DB:
create table `tabMachine Strip Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`stripped_item_code` varchar(140),
`stripped_serial_no` varchar(140),
`target_item_code` varchar(140),
`target_serial_no` varchar(140),
`stripping_reason` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,707 WARNING database DDL Query made to DB:
create table `tabPre Delivery Inspection Reading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`specification` varchar(140),
`value` varchar(140),
`status` varchar(140) default 'Accepted',
`sales_invoice` varchar(140),
`sales_order` varchar(140),
`sales_invoice_item` varchar(140),
`sales_order_item` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,778 WARNING database DDL Query made to DB:
create table `tabPre Delivery Inspection Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`quality_inspection_template_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,852 WARNING database DDL Query made to DB:
create table `tabPre Delivery Inspection - old` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`customer` varchar(140),
`verified_by` varchar(140),
`remarks` text,
`customer_remarks` text,
`customer_signature` longtext default 'Customer Signature',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,909 WARNING database DDL Query made to DB:
create table `tabSales Test` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`test` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:24,984 WARNING database DDL Query made to DB:
create table `tabRequested Funds Accounts Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_date` date,
`expense_type` varchar(140),
`request_description` varchar(140),
`request_amount` decimal(21,9) not null default 0,
`request_currency` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`invoice_number` varchar(140),
`quote_attachment` text,
`expense_account_currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 1.0,
`posting_date` date,
`expense_account` varchar(140),
`payable_account` varchar(140),
`payee` varchar(140),
`comment` varchar(140),
`requested_for` varchar(140),
`request_hidden_status` varchar(140),
`payable_account_currency` varchar(140),
`cost_center` varchar(140),
`reference` varchar(140),
`request_status` varchar(140) default 'open',
`requested_by` varchar(140),
`requested_on` datetime(6),
`recommended_by` varchar(140),
`recommended_on` datetime(6),
`recommender_comment` varchar(140),
`approved_by` varchar(140),
`approved_on` datetime(6),
`approver_comment` varchar(140),
`accounts_approved_by` varchar(140),
`accounts_approved_on` datetime(6),
`accounts_approver_comment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,052 WARNING database DDL Query made to DB:
create table `tabPre Delivery Inspection Old` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`customer` varchar(140),
`verified_by` varchar(140),
`remarks` text,
`customer_remarks` text,
`customer_signature` longtext default 'Customer Signature',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,185 WARNING database DDL Query made to DB:
create table `tabRequested Payments` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_date` date,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`approval_status` varchar(140),
`payment_status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,266 WARNING database DDL Query made to DB:
create table `tabRequested Funds Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_date` date,
`expense_type` varchar(140),
`request_description` varchar(140),
`request_amount` decimal(21,9) not null default 0,
`request_currency` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`invoice_number` varchar(140),
`quote_attachment` text,
`expense_account_currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 1.0,
`requested_by` varchar(140),
`requested_on` datetime(6),
`recommended_by` varchar(140),
`recommended_on` datetime(6),
`recommender_comment` varchar(140),
`approved_by` varchar(140),
`approved_on` datetime(6),
`approver_comment` varchar(140),
`accounts_approved_by` varchar(140),
`accounts_approved_on` datetime(6),
`accounts_approver_comment` varchar(140),
`posting_date` date,
`expense_account` varchar(140),
`payable_account` varchar(140),
`payee` varchar(140),
`comment` varchar(140),
`requested_for` varchar(140),
`request_hidden_status` varchar(140),
`payable_account_currency` varchar(140),
`cost_center` varchar(140),
`request_status` varchar(140) default 'open',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,354 WARNING database DDL Query made to DB:
create table `tabBond History Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`no_of_bundles` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,421 WARNING database DDL Query made to DB:
create table `tabCargo Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_number` varchar(140),
`container_size` varchar(140),
`seal_number` varchar(140),
`cargo_status` varchar(140),
`no_of_packages` int(11) not null default 0,
`gross_weight` decimal(21,9) not null default 0,
`net_weight` decimal(21,9) not null default 0,
`tare_weight` decimal(21,9) not null default 0,
`assigned_vehicle` varchar(140),
`assigned_driver` varchar(140),
`transport_status` int(11) not null default 0,
`bond_ref_no` varchar(140),
`bond_value` decimal(21,9) not null default 0,
`extra_details` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,485 WARNING database DDL Query made to DB:
create table `tabCargo Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cargo_name` varchar(140),
`local_export` int(1) not null default 0,
`transit_export` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,549 WARNING database DDL Query made to DB:
create table `tabLocations` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140),
`location_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,612 WARNING database DDL Query made to DB:
create table `tabBorder Procedure Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,672 WARNING database DDL Query made to DB:
create table `tabMandatory Attachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,733 WARNING database DDL Query made to DB:
create table `tabBond Reference Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`customs_ref_number` varchar(140),
`number_of_bundles` int(11) not null default 0,
`weight` decimal(21,9) not null default 0,
`bond_value` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,788 WARNING database DDL Query made to DB:
create table `tabTest Doctype` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`test1` varchar(140),
`test_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,852 WARNING database DDL Query made to DB:
create table `tabContainer Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{purpose}',
`naming_series` varchar(140),
`purpose` varchar(140),
`collection_date` date,
`shipping_line` varchar(140),
`export_reference` varchar(140),
`booking_number` varchar(140),
`amended_from` varchar(140),
`container_reference` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:25,953 WARNING database DDL Query made to DB:
create table `tabContainer Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_booking_no` varchar(140),
`to_booking_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,024 WARNING database DDL Query made to DB:
create table `tabShipping Line` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shipping_line_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,101 WARNING database DDL Query made to DB:
create table `tabContainer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_container_entry` varchar(140),
`creation_document_no` varchar(140),
`collection_date` date,
`shipping_line` varchar(140),
`export_reference` varchar(140),
`booking_number` varchar(140),
`container_no` varchar(140),
`container_type` varchar(140),
`container_size` varchar(140),
`seal_number` varchar(140),
`number_of_bundles` int(11) not null default 0,
`net_weight` decimal(21,9) not null default 0,
`gross_weight` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,211 WARNING database DDL Query made to DB:
create table `tabBorder Clearance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`billing_type` varchar(140),
`location` varchar(140),
`documents_received` datetime(6),
`crn_reference_number` varchar(140),
`no_of_borders` varchar(140),
`documents_submitted_by_driver` date,
`documents_submitted_by_driver_time` time(6),
`clearance_type` varchar(140),
`cargo_type` varchar(140),
`cargo_category` varchar(140),
`cargo_description` varchar(140),
`loose_cargo_category` varchar(140),
`goods_description` varchar(140),
`goods_quantity` decimal(21,9) not null default 0,
`goods_unit` varchar(140),
`loose_sub_t1_ref` varchar(140),
`loose_bond_value` varchar(140),
`loose_gross_weight` decimal(21,9) not null default 0,
`loose_net_weight` decimal(21,9) not null default 0,
`client` varchar(140),
`consignee` varchar(140),
`transporter_type` varchar(140),
`transporter_name` varchar(140),
`vehicle_plate_number` varchar(140),
`trailer_plate_number` varchar(140),
`driver_name` varchar(140),
`cargo_origin_country` varchar(140),
`cargo_origin_city` varchar(140),
`cargo_origin_exact` varchar(140),
`cargo_destination_country` varchar(140),
`cargo_destination_city` varchar(140),
`cargo_destination_exact` varchar(140),
`border1_name` varchar(140),
`border1_clearing_agent` varchar(140),
`border1_documents_submitted` datetime(6),
`border1_documents_received_by` varchar(140),
`border1_arrival` datetime(6),
`border1_departure` datetime(6),
`border1_crossing` datetime(6),
`border2_name` varchar(140),
`border2_clearing_agent` varchar(140),
`border2_documents_submitted` datetime(6),
`border2_documents_received_by` varchar(140),
`border2_arrival` datetime(6),
`border2_departure` datetime(6),
`border2_crossing` datetime(6),
`border3_name` varchar(140),
`border3_clearing_agent` varchar(140),
`border3_documents_submitted` datetime(6),
`border3_documents_received_by` varchar(140),
`border3_arrival` datetime(6),
`border3_departure` datetime(6),
`border3_crossing` datetime(6),
`border4_name` varchar(140),
`border4_clearing_agent` varchar(140),
`border4_documents_submitted` datetime(6),
`border4_documents_received_by` varchar(140),
`border4_arrival` datetime(6),
`border4_departure` datetime(6),
`border4_crossing` datetime(6),
`manifest_creation_date` date,
`manifest_approval_date` date,
`manifest_approval_time` time(6),
`customs_assessment_submited` date,
`final_assessment_received` date,
`tansad_number` varchar(140),
`verification_sealing_date` date,
`manifest_comparison_date` date,
`customs_release_date` date,
`lodge_for_t1` date,
`bt_approval_date` date,
`bond_ref_no` varchar(140),
`bond_value` varchar(140),
`bond_canceled_date` date,
`no_of_packages` varchar(140),
`offloading_point_arrival` datetime(6),
`offloading_datetime` datetime(6),
`closing_remarks` longtext,
`reference_trip_route` varchar(140),
`local_border` varchar(140),
`file_number` varchar(140),
`trip_reference_no` varchar(140),
`main_return_select` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,272 WARNING database DDL Query made to DB:
create table `tabICD` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`icd_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,338 WARNING database DDL Query made to DB:
create table `tabTerminal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`terminal` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,421 WARNING database DDL Query made to DB:
create table `tabBorder Procedures` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,499 WARNING database DDL Query made to DB:
create table `tabBorder Processing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_file` varchar(140),
`creation_document_no` varchar(140),
`creation_date` date,
`cross_border_no` varchar(140),
`customer` varchar(140),
`arrival_date` date,
`departure_date` date,
`crossing_date` date,
`bond_cancellation_date` date,
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,558 WARNING database DDL Query made to DB:
create table `tabContainer Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,621 WARNING database DDL Query made to DB:
create table `tabContainer Issue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{purpose}',
`naming_series` varchar(140),
`purpose` varchar(140),
`collection_date` date,
`shipping_line` varchar(140),
`export_reference` varchar(140),
`booking_number` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,682 WARNING database DDL Query made to DB:
create table `tabRequired Permit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`permit_name` varchar(140),
`mandatory` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,751 WARNING database DDL Query made to DB:
create table `tabImport File` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`import_file_number` varchar(140) unique,
`bl_number` varchar(140),
`no_of_containers` varchar(140),
`tansad_no` varchar(140),
`clearing_agent_name` varchar(140),
`supplier_name` varchar(140),
`supplier_invoice_no` varchar(140),
`invoice_currency` varchar(140),
`invoice_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,812 WARNING database DDL Query made to DB:
create table `tabPacking List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_number` varchar(140),
`seal_number` varchar(140),
`no_of_bundles` varchar(140),
`net_weight` decimal(21,9) not null default 0,
`gross_weight` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,869 WARNING database DDL Query made to DB:
create table `tabContainer Issue Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_no` varchar(140),
`seal_number` varchar(140),
`container_type` varchar(140),
`container_size` varchar(140),
`number_of_bundles` int(11) not null default 0,
`net_weight` decimal(21,9) not null default 0,
`gross_weight` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:26,980 WARNING database DDL Query made to DB:
create table `tabImport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_file_number` varchar(140),
`company` varchar(140),
`point_of_entry` varchar(140),
`bl_number` varchar(140),
`original_bl_received` date,
`location` varchar(140),
`documents_received_date` date,
`border_customer` varchar(140),
`reference_trip` varchar(140),
`cargo_type` varchar(140),
`shipper` varchar(140),
`consignee` varchar(140),
`notify_party` varchar(140),
`customer` varchar(140),
`clearing_agent_border_1` varchar(140),
`clearing_agent_border_2` varchar(140),
`clearing_agent_border_3` varchar(140),
`house_bill_of_lading` varchar(140),
`shipping_line` varchar(140),
`vessel_name` varchar(140),
`voyage` varchar(140),
`cargo_destination_country` varchar(140),
`cargo_destination_city` varchar(140),
`port_of_loading` varchar(140),
`port_of_discharge` varchar(140),
`eta` date,
`ata` date,
`discharge` date,
`terminal` varchar(140),
`icd` varchar(140),
`carry_in_date` date,
`demurrage_start_date` date,
`cargo` varchar(140),
`cargo_description` varchar(140),
`container_owner` varchar(140),
`amended_from` varchar(140),
`import_type` varchar(140),
`declaration_type` varchar(140),
`assessment_submission_date` date,
`manifest_date` date,
`tansad_number` varchar(140),
`tansad_date` date,
`manifest_comparison_date` date,
`final_assessment_obtained` date,
`lodged_for_verification` date,
`verification_date` date,
`customs_release_date` date,
`lodged_for_port_charges` date,
`port_charges_payment` date,
`transport_requested` varchar(140),
`t1_generated` date,
`t1_approved` date,
`bt_number` varchar(140),
`lodged_for_do_invoice` date,
`invoice_received` date,
`lodged_for_do` date,
`do_obtained` date,
`do_reference` varchar(140),
`storing_order_obtained` date,
`storing_order_validity` date,
`employee` varchar(140),
`employee_name` varchar(140),
`lodged_for_port_invoice` date,
`invoice_obtained` date,
`lodged_for_loading_permit` date,
`loading_permit_obtained` date,
`loading_date` date,
`loading_time` time(6),
`loading_permit_handover` date,
`loading_permit_handed_to_transporter_time` time(6),
`permit_received_by` varchar(140),
`special_instructions_to_transporter` text,
`status` varchar(140) default 'Open',
`cheque_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,041 WARNING database DDL Query made to DB:
create table `tabPort` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`port` varchar(140),
`country` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,101 WARNING database DDL Query made to DB:
create table `tabContainer Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_no` varchar(140),
`seal_number` varchar(140),
`container_type` varchar(140),
`container_size` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,158 WARNING database DDL Query made to DB:
create table `tabImport Border Procedure Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
`date` date,
`attachment` text,
`extra_comment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,220 WARNING database DDL Query made to DB:
create table `tabBond` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_no` varchar(140) unique,
`bond_value` decimal(21,9) not null default 0,
`no_of_packages` int(11) not null default 0,
`cargo` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,330 WARNING database DDL Query made to DB:
create table `tabExport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`booking_received` date,
`booking_number` varchar(140),
`export_type` varchar(140),
`shipper` varchar(140),
`notify_party` varchar(140),
`consignee` varchar(140),
`client` varchar(140),
`vessel_name` varchar(140),
`voyage_no` varchar(140),
`engagement_date` date,
`eta` date,
`cut_off_date` date,
`shipping_line` varchar(140),
`port_of_loading` varchar(140),
`loading_terminal` varchar(140),
`destination` varchar(140),
`origin` varchar(140),
`material` varchar(140),
`customs_assessment_submission` date,
`final_assessment_obtained` date,
`verification_date` date,
`customs_physical_verification` date,
`lodge_for_release` date,
`release_received` date,
`customs_release_date` date,
`bt_submission_date` date,
`bt_approval_date` date,
`bt_ref_no` varchar(140),
`bond_lodged_for_validation` datetime(6),
`bond_validated` datetime(6),
`customs_processing_type` varchar(140) default 'New System',
`stuffing_date` date,
`received_repacking_manifest` date,
`declaration_number_approval` date,
`packing_list_received` date,
`packing_list_received_by` varchar(140),
`loading_list_sent_new` date,
`lodge_port_permit` date,
`receive_port_permit` date,
`delivery_date` date,
`request_empty_container` date,
`empty_container_collected` date,
`loading_list_sent` date,
`declaration_number_received` date,
`declaration_number` varchar(140),
`vgm_submited` date,
`lodge_port_charges` date,
`invoice_received` date,
`invoice_number` varchar(140),
`port_charges_paid` date,
`transport_requested` date,
`lodge_loading_permit` date,
`loading_permit_received` date,
`loading_date` date,
`loading_time` time(6),
`loading_permit_handover_transporter` datetime(6),
`loading_permit_received_by` varchar(140),
`containers_arrived_at_port` date,
`file_submitted_to_scanner` date,
`container_scanned` date,
`scanner_report_collected` date,
`scanner_report_sent_to_client` date,
`scanner_report_sent_to_client_time` time(6),
`file_number` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Open',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,398 WARNING database DDL Query made to DB:
create table `tabContainer File Closing Information` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_no` varchar(140),
`proof_of_delivery` text,
`container_return_date` date,
`inward_interchange_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,461 WARNING database DDL Query made to DB:
create table `tabBorder Processing Vehicle Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`transporter` varchar(140),
`driver` varchar(140),
`driver_contact` varchar(140),
`vehicle_no` varchar(140),
`plate_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,589 WARNING database DDL Query made to DB:
create table `tabFiles` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`documents_received_date` date,
`location` varchar(140),
`customer` varchar(140),
`quotation` varchar(140),
`requested_service` varchar(140),
`import_transit` int(1) not null default 0,
`import_local` int(1) not null default 0,
`export_transit` int(1) not null default 0,
`export_local` int(1) not null default 0,
`transport_transit` int(1) not null default 0,
`transport_local` int(1) not null default 0,
`border_clearance` int(1) not null default 0,
`cross_border_no` varchar(140),
`border_processing_reference` varchar(140),
`bl_number` varchar(140),
`import_reference` varchar(140),
`booking_number` varchar(140),
`export_reference` varchar(140),
`source_country` varchar(140),
`source_city` varchar(140),
`destination_country` varchar(140),
`destination_city` varchar(140),
`transport_reference` varchar(140),
`number_of_borders` int(11) not null default 0,
`border_clearance_ref` varchar(140),
`per_unit` varchar(140),
`status` varchar(140) default 'Open',
`sales_order_reference` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,651 WARNING database DDL Query made to DB:
create table `tabReporting Status Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`datetime` datetime(6),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,706 WARNING database DDL Query made to DB:
create table `tabContainer Seals` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_container_entry` varchar(140),
`creation_document_no` varchar(140),
`collection_date` date,
`shipping_line` varchar(140),
`export_reference` varchar(140),
`booking_number` varchar(140),
`seal_number` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 18:30:27,764 WARNING database DDL Query made to DB:
create table `tabExpenses` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_date` date,
`expense_amount` decimal(21,9) not null default 0,
`expense_currency` varchar(140),
`expense_description` varchar(140),
`expense_receipt` text,
`expense_extra_details` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
