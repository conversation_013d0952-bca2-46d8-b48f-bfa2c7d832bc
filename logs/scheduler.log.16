2025-06-19 12:02:34,208 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:02:34,215 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:02:34,220 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:02:34,224 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 12:02:34,228 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for axessio
2025-06-19 12:02:34,232 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:02:34,236 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:02:34,244 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for axessio
2025-06-19 12:02:34,248 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 12:02:34,258 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for axessio
2025-06-19 12:02:34,262 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for axessio
2025-06-19 12:02:34,266 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for axessio
2025-06-19 12:02:34,270 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:02:34,277 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 12:02:34,284 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:02:34,287 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for axessio
2025-06-19 12:02:34,292 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:02:34,298 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:02:34,302 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 12:02:34,306 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:02:34,309 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:02:34,313 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:02:34,317 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for axessio
2025-06-19 12:02:34,331 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:02:34,340 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:02:34,351 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:02:34,354 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:02:34,357 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:02:34,361 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:02:34,365 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:03:34,398 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:03:34,400 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:03:34,408 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:03:34,415 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:03:34,422 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:03:34,428 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:03:34,431 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:03:34,441 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for axessio
2025-06-19 12:03:34,444 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:03:34,447 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:03:34,456 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:03:34,459 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:03:34,473 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:03:34,476 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for axessio
2025-06-19 12:03:34,483 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:03:34,490 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:03:34,497 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:03:34,500 ERROR scheduler Skipped queueing erpnext_germany.tasks.all because it was found in queue for axessio
2025-06-19 12:03:34,513 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for axessio
2025-06-19 12:03:34,517 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for axessio
2025-06-19 12:03:34,526 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:03:34,531 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:03:34,542 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for axessio
2025-06-19 12:03:34,547 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:03:34,550 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.hr_addon_settings.hr_addon_settings.send_work_anniversary_notification because it was found in queue for axessio
2025-06-19 12:03:34,564 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for axessio
2025-06-19 12:03:34,586 ERROR scheduler Skipped queueing print_designer.install.setup_chromium because it was found in queue for axessio
2025-06-19 12:03:34,589 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:03:34,604 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:03:34,612 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:03:34,625 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for axessio
2025-06-19 12:03:34,628 ERROR scheduler Skipped queueing active_users.utils.update.auto_check_for_update because it was found in queue for axessio
2025-06-19 12:03:34,631 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:03:34,643 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:03:34,648 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for axessio
2025-06-19 12:03:34,669 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:03:34,678 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:03:34,681 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for axessio
2025-06-19 12:03:34,699 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for axessio
2025-06-19 12:03:34,703 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for axessio
2025-06-19 12:03:34,708 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:03:34,712 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:03:34,724 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:03:34,745 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:03:34,752 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:03:34,755 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily because it was found in queue for axessio
2025-06-19 12:03:34,757 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:03:34,760 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:03:34,770 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:03:34,773 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:03:34,816 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:03:34,820 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:03:34,824 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:03:34,844 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:03:34,852 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:03:34,855 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:03:34,857 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:03:34,860 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:03:34,863 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:04:36,116 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for axessio
2025-06-19 12:04:36,120 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for axessio
2025-06-19 12:04:36,129 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:04:36,133 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:04:36,143 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:04:36,156 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:04:36,160 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for axessio
2025-06-19 12:04:36,163 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for axessio
2025-06-19 12:04:36,175 ERROR scheduler Skipped queueing insights.insights.doctype.insights_alert.insights_alert.send_alerts because it was found in queue for axessio
2025-06-19 12:04:36,184 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for axessio
2025-06-19 12:04:36,188 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:04:36,195 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for axessio
2025-06-19 12:04:36,206 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 12:04:36,211 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 12:04:36,214 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:04:36,218 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for axessio
2025-06-19 12:04:36,226 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:04:36,237 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for axessio
2025-06-19 12:04:36,241 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 12:04:36,245 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:04:36,249 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:04:36,253 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for axessio
2025-06-19 12:04:36,256 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:04:36,263 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:04:36,267 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:04:36,271 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:04:36,274 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for axessio
2025-06-19 12:04:36,278 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:04:36,292 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:04:36,299 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for axessio
2025-06-19 12:04:36,306 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:04:36,310 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.sms_notification.sms_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:04:36,317 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for axessio
2025-06-19 12:04:36,321 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:04:36,329 ERROR scheduler Skipped queueing erpnext_telegram_integration.extra_notifications.doctype.date_notification.date_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:04:36,332 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:04:36,336 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 12:04:36,340 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:04:36,343 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.workday.workday.generate_workdays_scheduled_job because it was found in queue for axessio
2025-06-19 12:04:36,347 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:04:36,354 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:04:36,358 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for axessio
2025-06-19 12:04:36,361 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:04:36,365 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.telegram_notification.telegram_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:04:36,369 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:04:36,372 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:04:36,376 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:04:36,379 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for axessio
2025-06-19 12:04:36,382 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:04:36,385 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for axessio
2025-06-19 12:04:36,388 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_all because it was found in queue for axessio
2025-06-19 12:04:36,396 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:04:36,399 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for axessio
2025-06-19 12:04:36,407 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:04:36,416 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:04:36,420 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 12:04:36,429 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:04:36,432 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for axessio
2025-06-19 12:04:36,435 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:04:36,438 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:04:36,441 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for axessio
2025-06-19 12:04:36,444 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:04:36,446 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:04:36,455 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for axessio
2025-06-19 12:04:36,458 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for axessio
2025-06-19 12:04:36,461 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for axessio
2025-06-19 12:04:36,464 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:04:36,468 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.sync_all_accounts_and_transactions because it was found in queue for axessio
2025-06-19 12:04:36,474 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:04:36,477 ERROR scheduler Skipped queueing drive.api.files.clear_deleted_files because it was found in queue for axessio
2025-06-19 12:04:36,486 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:04:36,490 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:04:36,494 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:04:36,498 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.intraday_sync_ebics because it was found in queue for axessio
2025-06-19 12:04:36,506 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for axessio
2025-06-19 12:04:36,511 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for axessio
2025-06-19 12:04:36,514 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly because it was found in queue for axessio
2025-06-19 12:04:36,517 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:04:36,520 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:04:36,523 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for axessio
2025-06-19 12:04:36,530 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:04:36,533 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:04:36,540 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:04:36,543 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:04:36,546 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:04:36,554 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for axessio
2025-06-19 12:04:36,557 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for axessio
2025-06-19 12:04:36,561 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 12:04:36,565 ERROR scheduler Skipped queueing drive.api.files.auto_delete_from_trash because it was found in queue for axessio
2025-06-19 12:04:36,568 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:04:36,571 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:04:36,575 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:04:36,580 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:04:36,603 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly because it was found in queue for axessio
2025-06-19 12:05:37,274 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:05:37,278 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:05:37,291 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:05:37,295 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:05:37,302 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:05:37,313 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:05:37,317 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:05:37,338 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:05:37,366 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:05:37,385 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:05:37,403 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:05:37,409 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:05:37,413 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:05:37,422 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:05:37,440 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:05:37,443 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:05:37,458 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:05:37,466 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:05:37,490 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:05:37,518 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:05:37,526 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:05:37,531 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:05:37,535 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:05:37,567 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:05:37,578 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:05:37,597 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:05:37,601 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:05:37,604 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:05:37,615 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:05:37,642 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:05:37,646 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:05:37,649 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:05:37,672 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:05:37,696 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:05:37,700 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:05:37,710 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:05:37,728 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:05:37,753 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:05:37,763 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:05:37,766 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:05:37,775 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:05:37,779 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:06:37,830 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for axessio
2025-06-19 12:06:37,834 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:06:37,839 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for axessio
2025-06-19 12:06:37,842 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:06:37,845 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:06:37,849 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for axessio
2025-06-19 12:06:37,852 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for axessio
2025-06-19 12:06:37,855 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:06:37,863 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.intraday_sync_ebics because it was found in queue for axessio
2025-06-19 12:06:37,867 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 12:06:37,871 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 12:06:37,875 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:06:37,879 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:06:37,883 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for axessio
2025-06-19 12:06:37,886 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:06:37,890 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_all because it was found in queue for axessio
2025-06-19 12:06:37,895 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:06:37,900 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for axessio
2025-06-19 12:06:37,903 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:06:37,906 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly because it was found in queue for axessio
2025-06-19 12:06:37,909 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for axessio
2025-06-19 12:06:37,913 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:06:37,916 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:06:37,921 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily because it was found in queue for axessio
2025-06-19 12:06:37,925 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:06:37,928 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for axessio
2025-06-19 12:06:37,932 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for axessio
2025-06-19 12:06:37,936 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:06:37,939 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for axessio
2025-06-19 12:06:37,942 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:06:37,945 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:06:37,949 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:06:37,952 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:06:37,955 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for axessio
2025-06-19 12:06:37,958 ERROR scheduler Skipped queueing insights.insights.doctype.insights_alert.insights_alert.send_alerts because it was found in queue for axessio
2025-06-19 12:06:37,962 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for axessio
2025-06-19 12:06:37,966 ERROR scheduler Skipped queueing erpnext_germany.tasks.all because it was found in queue for axessio
2025-06-19 12:06:37,970 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for axessio
2025-06-19 12:06:37,975 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:06:37,978 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:06:37,981 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:06:37,984 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.hr_addon_settings.hr_addon_settings.send_work_anniversary_notification because it was found in queue for axessio
2025-06-19 12:06:37,987 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for axessio
2025-06-19 12:06:37,990 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:06:37,993 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for axessio
2025-06-19 12:06:37,997 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:06:38,002 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:06:38,006 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for axessio
2025-06-19 12:06:38,010 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:06:38,013 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for axessio
2025-06-19 12:06:38,018 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:06:38,021 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for axessio
2025-06-19 12:06:38,025 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for axessio
2025-06-19 12:06:38,029 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for axessio
2025-06-19 12:06:38,034 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:06:38,039 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.sms_notification.sms_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:06:38,043 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:06:38,047 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:06:38,051 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for axessio
2025-06-19 12:06:38,056 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:06:38,060 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for axessio
2025-06-19 12:06:38,065 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 12:06:38,069 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 12:06:38,072 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:06:38,078 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:06:38,083 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:06:38,086 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.sync_all_accounts_and_transactions because it was found in queue for axessio
2025-06-19 12:06:38,090 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for axessio
2025-06-19 12:06:38,095 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 12:06:38,099 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for axessio
2025-06-19 12:06:38,105 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for axessio
2025-06-19 12:06:38,109 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for axessio
2025-06-19 12:06:38,112 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:06:38,116 ERROR scheduler Skipped queueing print_designer.install.setup_chromium because it was found in queue for axessio
2025-06-19 12:06:38,120 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 12:06:38,123 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:06:38,128 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for axessio
2025-06-19 12:06:38,133 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 12:06:38,137 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for axessio
2025-06-19 12:06:38,141 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:06:38,145 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:06:38,150 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:06:38,157 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for axessio
2025-06-19 12:06:38,162 ERROR scheduler Skipped queueing drive.api.files.auto_delete_from_trash because it was found in queue for axessio
2025-06-19 12:06:38,166 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for axessio
2025-06-19 12:06:38,170 ERROR scheduler Skipped queueing drive.api.files.clear_deleted_files because it was found in queue for axessio
2025-06-19 12:06:38,174 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:06:38,179 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:06:38,182 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for axessio
2025-06-19 12:06:38,186 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:06:38,191 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for axessio
2025-06-19 12:06:38,195 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for axessio
2025-06-19 12:06:38,200 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:06:38,205 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for axessio
2025-06-19 12:06:38,210 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:06:38,214 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 12:06:38,217 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for axessio
2025-06-19 12:06:38,221 ERROR scheduler Skipped queueing erpnext_telegram_integration.extra_notifications.doctype.date_notification.date_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:06:38,224 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:06:38,228 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for axessio
2025-06-19 12:06:38,231 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.workday.workday.generate_workdays_scheduled_job because it was found in queue for axessio
2025-06-19 12:06:38,234 ERROR scheduler Skipped queueing invoice_schedule_cron because it was found in queue for axessio
2025-06-19 12:06:38,239 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for axessio
2025-06-19 12:06:38,242 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for axessio
2025-06-19 12:06:38,246 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:06:38,250 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for axessio
2025-06-19 12:06:38,253 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for axessio
2025-06-19 12:06:38,256 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:06:38,262 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:06:38,267 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.telegram_notification.telegram_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:06:38,272 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for axessio
2025-06-19 12:06:38,278 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:06:38,282 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:06:38,287 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for axessio
2025-06-19 12:06:38,291 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for axessio
2025-06-19 12:06:38,298 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for axessio
2025-06-19 12:06:38,302 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:06:38,306 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for axessio
2025-06-19 12:06:38,310 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for axessio
2025-06-19 12:06:38,314 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly because it was found in queue for axessio
2025-06-19 12:06:38,317 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:06:38,321 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:06:38,325 ERROR scheduler Skipped queueing active_users.utils.update.auto_check_for_update because it was found in queue for axessio
2025-06-19 12:06:38,329 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for axessio
2025-06-19 12:07:39,497 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:07:39,500 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for axessio
2025-06-19 12:07:39,506 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:07:39,509 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for axessio
2025-06-19 12:07:39,512 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.intraday_sync_ebics because it was found in queue for axessio
2025-06-19 12:07:39,516 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for axessio
2025-06-19 12:07:39,519 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for axessio
2025-06-19 12:07:39,522 ERROR scheduler Skipped queueing drive.api.files.auto_delete_from_trash because it was found in queue for axessio
2025-06-19 12:07:39,525 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for axessio
2025-06-19 12:07:39,528 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 12:07:39,531 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:07:39,534 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:07:39,537 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for axessio
2025-06-19 12:07:39,540 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for axessio
2025-06-19 12:07:39,544 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for axessio
2025-06-19 12:07:39,549 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly because it was found in queue for axessio
2025-06-19 12:07:39,552 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:07:39,555 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:07:39,558 ERROR scheduler Skipped queueing erpnext_germany.tasks.all because it was found in queue for axessio
2025-06-19 12:07:39,562 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for axessio
2025-06-19 12:07:39,565 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 12:07:39,569 ERROR scheduler Skipped queueing drive.api.files.clear_deleted_files because it was found in queue for axessio
2025-06-19 12:07:39,573 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for axessio
2025-06-19 12:07:39,578 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:07:39,581 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for axessio
2025-06-19 12:07:39,585 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:07:39,589 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:07:39,592 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:07:39,596 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:07:39,599 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for axessio
2025-06-19 12:07:39,603 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for axessio
2025-06-19 12:07:39,607 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:07:39,611 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for axessio
2025-06-19 12:07:39,615 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for axessio
2025-06-19 12:07:39,619 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:07:39,623 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:07:39,626 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.hr_addon_settings.hr_addon_settings.send_work_anniversary_notification because it was found in queue for axessio
2025-06-19 12:07:39,630 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for axessio
2025-06-19 12:07:39,633 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for axessio
2025-06-19 12:07:39,637 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:07:39,641 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.telegram_notification.telegram_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:07:39,644 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for axessio
2025-06-19 12:07:39,648 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily because it was found in queue for axessio
2025-06-19 12:07:39,652 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:07:39,655 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:07:39,658 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:07:39,662 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:07:39,666 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for axessio
2025-06-19 12:07:39,670 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:07:39,674 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for axessio
2025-06-19 12:07:39,678 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:07:39,681 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for axessio
2025-06-19 12:07:39,684 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for axessio
2025-06-19 12:07:39,687 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for axessio
2025-06-19 12:07:39,691 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for axessio
2025-06-19 12:07:39,695 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:07:39,699 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:07:39,703 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for axessio
2025-06-19 12:07:39,710 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 12:07:39,714 ERROR scheduler Skipped queueing invoice_schedule_cron because it was found in queue for axessio
2025-06-19 12:07:39,718 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 12:07:39,722 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly because it was found in queue for axessio
2025-06-19 12:07:39,725 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:07:39,728 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 12:07:39,731 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for axessio
2025-06-19 12:07:39,734 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for axessio
2025-06-19 12:07:39,737 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:07:39,740 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:07:39,743 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for axessio
2025-06-19 12:07:39,746 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:07:39,749 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:07:39,752 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for axessio
2025-06-19 12:07:39,755 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:07:39,758 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.sms_notification.sms_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:07:39,762 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.sync_all_accounts_and_transactions because it was found in queue for axessio
2025-06-19 12:07:39,765 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for axessio
2025-06-19 12:07:39,768 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for axessio
2025-06-19 12:07:39,771 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:07:39,774 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for axessio
2025-06-19 12:07:39,777 ERROR scheduler Skipped queueing erpnext_telegram_integration.extra_notifications.doctype.date_notification.date_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:07:39,780 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for axessio
2025-06-19 12:07:39,785 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:07:39,788 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:07:39,791 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:07:39,794 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:07:39,801 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:07:39,804 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:07:39,807 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for axessio
2025-06-19 12:07:39,811 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 12:07:39,815 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:07:39,819 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:07:39,822 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for axessio
2025-06-19 12:07:39,825 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:07:39,829 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for axessio
2025-06-19 12:07:39,832 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:07:39,836 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:07:39,840 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_all because it was found in queue for axessio
2025-06-19 12:07:39,843 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:07:39,846 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for axessio
2025-06-19 12:07:39,849 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.workday.workday.generate_workdays_scheduled_job because it was found in queue for axessio
2025-06-19 12:07:39,852 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for axessio
2025-06-19 12:07:39,855 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for axessio
2025-06-19 12:07:39,859 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for axessio
2025-06-19 12:07:39,862 ERROR scheduler Skipped queueing insights.insights.doctype.insights_alert.insights_alert.send_alerts because it was found in queue for axessio
2025-06-19 12:07:39,866 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for axessio
2025-06-19 12:07:39,869 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:07:39,871 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:07:39,875 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:07:39,878 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 12:07:39,883 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for axessio
2025-06-19 12:07:39,886 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:07:39,888 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for axessio
2025-06-19 12:07:39,891 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 12:07:39,894 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:07:39,897 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for axessio
2025-06-19 12:07:39,900 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for axessio
2025-06-19 12:07:39,905 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for axessio
2025-06-19 12:07:39,908 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:07:39,910 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:07:39,914 ERROR scheduler Skipped queueing print_designer.install.setup_chromium because it was found in queue for axessio
2025-06-19 12:07:39,918 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:07:39,921 ERROR scheduler Skipped queueing active_users.utils.update.auto_check_for_update because it was found in queue for axessio
2025-06-19 12:07:39,925 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:07:39,928 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for axessio
2025-06-19 12:08:40,246 ERROR scheduler Skipped queueing frappe_whatsapp.frappe_whatsapp.doctype.whatsapp_notification.whatsapp_notification.trigger_notifications because it was found in queue for axessio
2025-06-19 12:08:40,249 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeBeforeLeaseExpire because it was found in queue for axessio
2025-06-19 12:08:40,254 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for axessio
2025-06-19 12:08:40,258 ERROR scheduler Skipped queueing propms.auto_custom.statusChangeAfterLeaseExpire because it was found in queue for axessio
2025-06-19 12:08:40,262 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for axessio
2025-06-19 12:08:40,266 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for axessio
2025-06-19 12:08:40,269 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for axessio
2025-06-19 12:08:40,272 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for axessio
2025-06-19 12:08:40,275 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for axessio
2025-06-19 12:08:40,279 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for axessio
2025-06-19 12:08:40,282 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:08:40,285 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for axessio
2025-06-19 12:08:40,288 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for axessio
2025-06-19 12:08:40,291 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for axessio
2025-06-19 12:08:40,294 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for axessio
2025-06-19 12:08:40,298 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for axessio
2025-06-19 12:08:40,302 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for axessio
2025-06-19 12:08:40,306 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for axessio
2025-06-19 12:08:40,309 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for axessio
2025-06-19 12:08:40,314 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for axessio
2025-06-19 12:08:40,317 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for axessio
2025-06-19 12:08:40,321 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for axessio
2025-06-19 12:08:40,324 ERROR scheduler Skipped queueing print_designer.install.setup_chromium because it was found in queue for axessio
2025-06-19 12:08:40,328 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for axessio
2025-06-19 12:08:40,332 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for axessio
2025-06-19 12:08:40,336 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for axessio
2025-06-19 12:08:40,340 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for axessio
2025-06-19 12:08:40,343 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for axessio
2025-06-19 12:08:40,348 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for axessio
2025-06-19 12:08:40,351 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for axessio
2025-06-19 12:08:40,354 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for axessio
2025-06-19 12:08:40,357 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:08:40,360 ERROR scheduler Skipped queueing erpnext_germany.tasks.all because it was found in queue for axessio
2025-06-19 12:08:40,364 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for axessio
2025-06-19 12:08:40,367 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily because it was found in queue for axessio
2025-06-19 12:08:40,370 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:08:40,374 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for axessio
2025-06-19 12:08:40,377 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for axessio
2025-06-19 12:08:40,381 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.workday.workday.generate_workdays_scheduled_job because it was found in queue for axessio
2025-06-19 12:08:40,386 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_daily_long because it was found in queue for axessio
2025-06-19 12:08:40,389 ERROR scheduler Skipped queueing active_users.utils.update.auto_check_for_update because it was found in queue for axessio
2025-06-19 12:08:40,393 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for axessio
2025-06-19 12:08:40,397 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.sms_notification.sms_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:08:40,402 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_all because it was found in queue for axessio
2025-06-19 12:08:40,405 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for axessio
2025-06-19 12:08:40,408 ERROR scheduler Skipped queueing invoice_schedule_cron because it was found in queue for axessio
2025-06-19 12:08:40,412 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for axessio
2025-06-19 12:08:40,417 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:08:40,423 ERROR scheduler Skipped queueing propms.lease_invoice_schedule.make_lease_invoice_schedule because it was found in queue for axessio
2025-06-19 12:08:40,427 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for axessio
2025-06-19 12:08:40,432 ERROR scheduler Skipped queueing insights.insights.doctype.insights_alert.insights_alert.send_alerts because it was found in queue for axessio
2025-06-19 12:08:40,435 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for axessio
2025-06-19 12:08:40,438 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for axessio
2025-06-19 12:08:40,441 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for axessio
2025-06-19 12:08:40,444 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for axessio
2025-06-19 12:08:40,448 ERROR scheduler Skipped queueing hr_addon.hr_addon.doctype.hr_addon_settings.hr_addon_settings.send_work_anniversary_notification because it was found in queue for axessio
2025-06-19 12:08:40,452 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for axessio
2025-06-19 12:08:40,456 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for axessio
2025-06-19 12:08:40,459 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for axessio
2025-06-19 12:08:40,464 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for axessio
2025-06-19 12:08:40,468 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for axessio
2025-06-19 12:08:40,472 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for axessio
2025-06-19 12:08:40,475 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for axessio
2025-06-19 12:08:40,478 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.intraday_sync_ebics because it was found in queue for axessio
2025-06-19 12:08:40,481 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for axessio
2025-06-19 12:08:40,484 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for axessio
2025-06-19 12:08:40,487 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for axessio
2025-06-19 12:08:40,491 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for axessio
2025-06-19 12:08:40,494 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:08:40,497 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for axessio
2025-06-19 12:08:40,500 ERROR scheduler Skipped queueing erpnext_telegram_integration.extra_notifications.doctype.date_notification.date_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:08:40,503 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for axessio
2025-06-19 12:08:40,507 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for axessio
2025-06-19 12:08:40,510 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for axessio
2025-06-19 12:08:40,513 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
2025-06-19 12:08:40,516 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for axessio
2025-06-19 12:08:40,519 ERROR scheduler Skipped queueing drive.api.files.auto_delete_from_trash because it was found in queue for axessio
2025-06-19 12:08:40,522 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:08:40,525 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for axessio
2025-06-19 12:08:40,528 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for axessio
2025-06-19 12:08:40,531 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for axessio
2025-06-19 12:08:40,534 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for axessio
2025-06-19 12:08:40,537 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for axessio
2025-06-19 12:08:40,541 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for axessio
2025-06-19 12:08:40,545 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for axessio
2025-06-19 12:08:40,549 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for axessio
2025-06-19 12:08:40,554 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:08:40,557 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for axessio
2025-06-19 12:08:40,560 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for axessio
2025-06-19 12:08:40,563 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for axessio
2025-06-19 12:08:40,566 ERROR scheduler Skipped queueing propms.lease_invoice.leaseInvoiceAutoCreate because it was found in queue for axessio
2025-06-19 12:08:40,569 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for axessio
2025-06-19 12:08:40,573 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for axessio
2025-06-19 12:08:40,576 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for axessio
2025-06-19 12:08:40,579 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for axessio
2025-06-19 12:08:40,583 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for axessio
2025-06-19 12:08:40,586 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:08:40,590 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for axessio
2025-06-19 12:08:40,593 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for axessio
2025-06-19 12:08:40,597 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for axessio
2025-06-19 12:08:40,602 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for axessio
2025-06-19 12:08:40,606 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for axessio
2025-06-19 12:08:40,613 ERROR scheduler Skipped queueing drive.api.files.clear_deleted_files because it was found in queue for axessio
2025-06-19 12:08:40,617 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for axessio
2025-06-19 12:08:40,621 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for axessio
2025-06-19 12:08:40,624 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for axessio
2025-06-19 12:08:40,627 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for axessio
2025-06-19 12:08:40,629 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly because it was found in queue for axessio
2025-06-19 12:08:40,632 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for axessio
2025-06-19 12:08:40,635 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_hourly_long because it was found in queue for axessio
2025-06-19 12:08:40,640 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for axessio
2025-06-19 12:08:40,644 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for axessio
2025-06-19 12:08:40,647 ERROR scheduler Skipped queueing erpnext_telegram_integration.erpnext_telegram_integration.doctype.telegram_notification.telegram_notification.trigger_daily_alerts because it was found in queue for axessio
2025-06-19 12:08:40,650 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for axessio
2025-06-19 12:08:40,654 ERROR scheduler Skipped queueing banking.klarna_kosma_integration.doctype.banking_settings.banking_settings.sync_all_accounts_and_transactions because it was found in queue for axessio
2025-06-19 12:08:40,658 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly because it was found in queue for axessio
2025-06-19 12:08:40,661 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for axessio
2025-06-19 12:08:40,665 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for axessio
2025-06-19 12:08:40,670 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for axessio
2025-06-19 12:08:40,673 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for axessio
2025-06-19 12:08:40,678 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for axessio
2025-06-19 12:08:40,681 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for axessio
2025-06-19 12:08:40,685 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for axessio
2025-06-19 12:08:40,689 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for axessio
2025-06-19 12:09:41,774 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for axessio
2025-06-19 12:09:41,779 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for axessio
2025-06-19 12:09:41,784 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for axessio
2025-06-19 12:09:41,791 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for axessio
2025-06-19 12:09:41,798 ERROR scheduler Skipped queueing frappe_whatsapp.utils.trigger_whatsapp_notifications_weekly_long because it was found in queue for axessio
