2025-06-19 17:54:05,045 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 17:55:05,466 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 17:56:05,724 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 17:57:05,887 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 17:58:06,452 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 17:59:06,457 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:00:08,268 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:01:09,020 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:02:09,794 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:03:09,801 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:04:11,176 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:05:11,734 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:06:12,263 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:07:12,760 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:08:14,211 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:09:14,743 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:10:15,131 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:11:16,071 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:12:17,377 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:13:17,998 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:14:18,087 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:15:18,466 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:16:18,862 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:17:19,365 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:18:19,754 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:19:20,379 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:20:20,457 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:21:21,067 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:22:21,234 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:23:21,877 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:24:21,884 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:25:22,504 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:26:22,933 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:27:22,941 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:28:23,263 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
2025-06-19 18:29:24,104 ERROR scheduler Exception in Enqueue Events for Site viva
Traceback (most recent call last):
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
