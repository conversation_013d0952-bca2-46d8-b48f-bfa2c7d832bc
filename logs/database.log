2025-06-18 16:32:44,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `first_response_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9)
2025-06-18 16:32:45,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `lease` varchar(140), ADD COLUMN `lease_item` varchar(140), ADD COLUMN `job_card` varchar(140)
2025-06-18 16:32:45,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIF<PERSON> `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0
2025-06-18 16:32:45,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `security_account_code` varchar(140), ADD COLUMN `default_tax_account_head` varchar(140), ADD COLUMN `default_tax_template` varchar(140), ADD COLUMN `default_maintenance_tax_template` varchar(140)
2025-06-18 16:32:45,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-06-18 16:32:45,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` ADD COLUMN `material_request` varchar(140)
2025-06-18 16:32:45,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-06-18 16:32:45,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabProperty` ADD COLUMN `territory` varchar(140)
2025-06-18 16:32:45,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabProperty` MODIFY `security_deposit` decimal(21,9) not null default 0, MODIFY `carpet_area` decimal(21,9) not null default 0, MODIFY `rent` decimal(21,9) not null default 0, MODIFY `builtup_area` decimal(21,9) not null default 0
2025-06-18 16:49:12,789 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_9c5dc86bed914016'@'localhost'
2025-06-18 16:49:22,654 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_9c5dc86bed914016`
2025-06-18 16:49:22,658 WARNING database DDL Query made to DB:
CREATE USER '_9c5dc86bed914016'@'localhost' IDENTIFIED BY '0X93uAF1GqUg3Odh'
2025-06-18 16:49:22,659 WARNING database DDL Query made to DB:
CREATE DATABASE `_9c5dc86bed914016` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-18 16:52:44,420 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_9c5dc86bed914016'@'localhost'
2025-06-18 16:52:45,874 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_9c5dc86bed914016`
2025-06-18 16:52:45,877 WARNING database DDL Query made to DB:
CREATE USER '_9c5dc86bed914016'@'localhost' IDENTIFIED BY '0X93uAF1GqUg3Odh'
2025-06-18 16:52:45,879 WARNING database DDL Query made to DB:
CREATE DATABASE `_9c5dc86bed914016` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
