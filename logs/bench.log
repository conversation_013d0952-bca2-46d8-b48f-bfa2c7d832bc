2025-06-04 16:01:34,685 DEBUG cd frappe-bench && python3 -m venv env
2025-06-04 16:01:36,750 DEBUG cd frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-06-04 16:01:38,028 DEBUG cd frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet wheel
2025-06-04 16:01:41,213 LOG Getting frappe
2025-06-04 16:01:41,213 DEBUG cd frappe-bench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-06-04 16:02:40,729 LOG Installing frappe
2025-06-04 16:02:40,730 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/frappe 
2025-06-04 16:03:26,751 DEBUG cd /home/<USER>/dev/frappe-bench/apps/frappe && yarn install --check-files
2025-06-04 16:04:33,267 DEBUG cd frappe-bench && bench build
2025-06-04 16:04:33,348 INFO /home/<USER>/dev/dev/bin/bench build
2025-06-04 16:04:45,516 LOG setting up backups
2025-06-04 16:04:45,532 LOG backups were set up
2025-06-04 16:04:45,532 INFO Bench frappe-bench initialized
2025-06-04 16:08:54,997 INFO /home/<USER>/dev/dev/bin/bench new-site site1
2025-06-04 16:09:58,252 INFO /home/<USER>/dev/dev/bin/bench get-app payments --branch version-15
2025-06-04 16:09:59,434 LOG Getting payments
2025-06-04 16:09:59,434 DEBUG cd ./apps && git clone https://github.com/frappe/payments.git --branch version-15 --depth 1 --origin upstream
2025-06-04 16:10:03,458 LOG Installing payments
2025-06-04 16:10:03,458 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/payments 
2025-06-04 16:10:12,189 DEBUG bench build --app payments
2025-06-04 16:10:12,272 INFO /home/<USER>/dev/dev/bin/bench build --app payments
2025-06-04 16:10:20,892 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 erpnext
2025-06-04 16:10:21,861 LOG Getting erpnext
2025-06-04 16:10:21,861 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-06-04 16:10:46,895 LOG Installing erpnext
2025-06-04 16:10:46,895 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/erpnext 
2025-06-04 16:11:15,033 DEBUG cd /home/<USER>/dev/frappe-bench/apps/erpnext && yarn install --check-files
2025-06-04 16:11:15,781 DEBUG bench build --app erpnext
2025-06-04 16:11:15,861 INFO /home/<USER>/dev/dev/bin/bench build --app erpnext
2025-06-04 16:11:24,149 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 hrms
2025-06-04 16:11:25,450 LOG Getting hrms
2025-06-04 16:11:25,450 DEBUG cd ./apps && git clone https://github.com/frappe/hrms.git --branch version-15 --depth 1 --origin upstream
2025-06-04 16:11:30,357 LOG Installing hrms
2025-06-04 16:11:30,358 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/hrms 
2025-06-04 16:11:31,991 DEBUG cd /home/<USER>/dev/frappe-bench/apps/hrms && yarn install --check-files
2025-06-04 16:13:55,192 DEBUG bench build --app hrms
2025-06-04 16:13:55,274 INFO /home/<USER>/dev/dev/bin/bench build --app hrms
2025-06-04 16:15:19,879 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 healthcare
2025-06-04 16:15:20,735 LOG Getting healthcare
2025-06-04 16:15:20,735 DEBUG cd ./apps && git clone https://github.com/frappe/healthcare.git --branch version-15 --depth 1 --origin upstream
2025-06-04 16:15:26,535 LOG Installing healthcare
2025-06-04 16:15:26,535 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/healthcare 
2025-06-04 16:15:29,463 DEBUG bench build --app healthcare
2025-06-04 16:15:29,545 INFO /home/<USER>/dev/dev/bin/bench build --app healthcare
2025-06-04 16:19:53,862 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 https://github.com/Aakvatech-Limited/csf_tz.git
2025-06-04 16:19:53,867 LOG Getting csf_tz
2025-06-04 16:19:53,867 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-06-04 16:20:00,763 LOG Installing csf_tz
2025-06-04 16:20:00,764 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/csf_tz 
2025-06-04 16:20:45,818 DEBUG cd /home/<USER>/dev/frappe-bench/apps/csf_tz && yarn install --check-files
2025-06-04 16:20:49,058 DEBUG bench build --app csf_tz
2025-06-04 16:20:49,139 INFO /home/<USER>/dev/dev/bin/bench build --app csf_tz
2025-06-04 16:21:06,428 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 https://github.com/Aakvatech-Limited/PropMS.git
2025-06-04 16:21:06,434 LOG Getting PropMS
2025-06-04 16:21:06,434 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/PropMS.git --branch version-15 --depth 1 --origin upstream
2025-06-04 16:21:09,850 LOG Installing propms
2025-06-04 16:21:09,851 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/propms 
2025-06-04 16:21:13,455 DEBUG bench build --app propms
2025-06-04 16:21:13,536 INFO /home/<USER>/dev/dev/bin/bench build --app propms
2025-06-04 16:29:38,323 INFO /home/<USER>/dev/dev/bin/bench get-app --branch v14_nhof_test https://github.com/Aakvatech-Limited/HMS_TZ.git
2025-06-04 16:29:38,328 LOG Getting HMS_TZ
2025-06-04 16:29:38,329 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/HMS_TZ.git --branch v14_nhof_test --depth 1 --origin upstream
2025-06-04 16:29:39,662 WARNING cd ./apps && git clone https://github.com/Aakvatech-Limited/HMS_TZ.git --branch v14_nhof_test --depth 1 --origin upstream executed with exit code 128
2025-06-04 16:29:39,663 WARNING /home/<USER>/dev/dev/bin/bench get-app --branch v14_nhof_test https://github.com/Aakvatech-Limited/HMS_TZ.git executed with exit code 1
2025-06-04 16:29:53,533 INFO /home/<USER>/dev/dev/bin/bench get-app --branch v14_nhif_test https://github.com/Aakvatech-Limited/HMS_TZ.git
2025-06-04 16:29:53,539 LOG Getting HMS_TZ
2025-06-04 16:29:53,539 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/HMS_TZ.git --branch v14_nhif_test --depth 1 --origin upstream
2025-06-04 16:29:56,323 LOG Installing hms_tz
2025-06-04 16:29:56,324 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/hms_tz 
2025-06-04 16:30:14,992 DEBUG cd /home/<USER>/dev/frappe-bench/apps/hms_tz && yarn install --check-files
2025-06-04 16:30:20,260 DEBUG bench build --app hms_tz
2025-06-04 16:30:20,343 INFO /home/<USER>/dev/dev/bin/bench build --app hms_tz
2025-06-04 16:31:16,222 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-04 16:33:16,797 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-04 16:37:44,144 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-04 16:38:20,961 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-04 16:38:21,121 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-04 16:38:21,126 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-04 16:38:21,127 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-04 16:38:21,168 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-04 16:38:35,422 INFO /home/<USER>/dev/dev/bin/bench --site site1 add-to-hosts
2025-06-04 16:39:36,028 INFO /home/<USER>/dev/dev/bin/bench --site site1 add-to-hosts
2025-06-04 16:39:40,490 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-04 16:39:40,644 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-04 16:39:40,654 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-04 16:39:40,676 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-04 16:39:40,687 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-04 17:53:26,529 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app propms
2025-06-04 17:53:38,743 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-04 17:53:38,911 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-04 17:53:38,926 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-04 17:53:38,931 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-04 17:53:38,952 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-04 18:00:01,449 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-05 08:36:41,469 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-05 08:36:41,634 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-05 08:36:41,636 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-05 08:36:41,669 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-05 08:36:41,672 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-05 08:37:26,779 INFO /home/<USER>/dev/dev/bin/bench new-site health
2025-06-05 08:39:02,347 INFO /home/<USER>/dev/dev/bin/bench --site install-app csf_tz
2025-06-05 08:39:17,453 INFO /home/<USER>/dev/dev/bin/bench --site health install-app csf_tz
2025-06-05 08:40:23,068 INFO /home/<USER>/dev/dev/bin/bench --site health install-app csf_tz
2025-06-05 08:41:03,113 INFO /home/<USER>/dev/dev/bin/bench --site health install-app healthcare
2025-06-05 08:41:26,868 INFO /home/<USER>/dev/dev/bin/bench --site health install-app hms_tz
2025-06-05 08:41:55,967 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-05 08:41:56,121 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-05 08:41:56,122 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-05 08:41:56,162 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-05 08:41:56,166 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-05 08:42:38,523 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-05 08:46:11,973 INFO /home/<USER>/dev/dev/bin/bench --site health add-to-hosts
2025-06-05 08:48:42,068 INFO /home/<USER>/dev/dev/bin/bench --site health remove-from-installed-apps csf_tz
2025-06-05 08:48:45,675 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-05 10:21:32,865 INFO /home/<USER>/dev/dev/bin/bench execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 10:21:47,339 INFO /home/<USER>/dev/dev/bin/bench --ste site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 10:22:01,505 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 10:23:16,698 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 10:24:25,080 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 10:24:46,880 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 10:25:00,287 INFO /home/<USER>/dev/dev/bin/bench --site site migrate
2025-06-05 10:25:08,673 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-05 10:25:38,868 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 10:26:21,345 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate --skip-failling
2025-06-05 10:40:52,345 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate --skip-failing
2025-06-05 11:16:05,091 INFO /home/<USER>/dev/dev/bin/bench --site site1 remove-from-installed-apps csf_tz
2025-06-05 11:16:11,642 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-05 11:16:43,387 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:17:58,017 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:19:12,923 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:19:30,406 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:20:17,582 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:20:47,523 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:21:03,955 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:21:11,917 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:21:50,590 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 11:23:36,636 INFO /home/<USER>/dev/dev/bin/bench --site health install-app csf_tz
2025-06-05 11:23:46,219 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-05 11:23:49,441 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-05 11:24:15,901 INFO /home/<USER>/dev/dev/bin/bench --site site1 reinstall
2025-06-05 11:26:13,458 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-05 11:26:46,394 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app propms
2025-06-05 11:27:03,598 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-05 11:27:03,779 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-05 11:27:03,785 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-05 11:27:03,797 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-05 11:27:03,800 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-05 11:34:21,781 INFO /home/<USER>/dev/dev/bin/bench clear-cache
2025-06-05 11:34:30,940 INFO /home/<USER>/dev/dev/bin/bench --site site1 clear-cache
2025-06-05 11:34:34,158 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-05 11:34:34,359 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-05 11:34:34,396 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-05 11:34:34,406 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-05 11:34:34,407 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-05 11:35:25,176 INFO /home/<USER>/dev/dev/bin/bench --site site1 clear-cache
2025-06-05 11:35:27,221 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-05 11:35:27,377 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-05 11:35:27,382 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-05 11:35:27,421 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-05 11:35:27,422 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-05 12:00:01,984 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-05 12:03:44,488 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 16:04:38,622 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 16:05:19,818 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 16:05:48,823 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 16:06:00,269 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 16:06:17,790 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 16:06:25,591 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-05 16:06:25,765 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-05 16:06:25,778 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-05 16:06:25,798 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-05 16:06:25,799 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-05 16:06:35,781 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-05 18:00:01,552 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-06 09:29:59,169 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-06 09:29:59,343 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-06 09:29:59,354 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-06 09:29:59,358 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-06 09:29:59,368 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-06 09:38:08,673 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 09:38:38,907 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 09:39:50,928 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 09:44:00,683 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 09:45:36,129 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 09:49:04,733 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 10:00:27,272 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 10:00:54,871 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 10:02:36,618 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 10:06:02,887 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:12:02,937 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:31:21,086 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:32:44,617 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:33:49,075 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:35:04,533 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:36:01,105 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:36:46,789 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-06 11:36:54,747 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-06 11:37:12,841 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:37:59,697 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:38:45,490 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 11:45:53,966 INFO /home/<USER>/dev/dev/bin/bench --site site1 remove-frm-installed-apps csf_tz
2025-06-06 11:46:09,565 INFO /home/<USER>/dev/dev/bin/bench --site site1 remove-from-installed-apps csf_tz
2025-06-06 11:46:13,088 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-06 11:46:31,067 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-06 12:00:01,536 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-06 18:00:01,726 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-07 18:00:11,250 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-09 08:29:24,523 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-09 08:29:24,722 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-09 08:29:24,736 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-09 08:29:24,778 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-09 08:29:24,778 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-09 08:29:52,507 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-09 08:30:22,573 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-09 08:31:19,285 INFO /home/<USER>/dev/dev/bin/bench --site site1 mariadb
2025-06-09 08:33:47,959 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-09 08:35:38,334 INFO /home/<USER>/dev/dev/bin/bench --site site1 mariadb
2025-06-09 08:35:51,411 INFO /home/<USER>/dev/dev/bin/bench --site site1 install-app csf_tz
2025-06-09 08:36:10,385 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-09 08:36:10,577 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-09 08:36:10,582 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-09 08:36:10,587 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-09 08:36:10,645 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-09 08:36:13,884 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-09 08:47:29,027 INFO /home/<USER>/dev/dev/bin/bench --site health install-app csf_tz
2025-06-09 08:47:46,443 INFO /home/<USER>/dev/dev/bin/bench --site health mariadb
2025-06-09 08:48:29,973 INFO /home/<USER>/dev/dev/bin/bench --site health install-app csf_tz
2025-06-09 08:48:48,738 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-09 08:48:48,891 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-09 08:48:48,923 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-09 08:48:48,928 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-09 08:48:48,940 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-09 08:48:59,934 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-09 10:37:45,882 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-09 10:40:35,310 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice.leaseInvoiceAutoCreate
2025-06-09 11:04:49,049 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-09 12:00:02,028 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-09 12:41:53,453 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-09 15:03:03,716 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-09 15:17:56,574 INFO /home/<USER>/dev/dev/bin/bench --site health list-apps
2025-06-09 15:18:51,689 INFO /home/<USER>/dev/dev/bin/bench --site health restore /home/<USER>/Downloads/20250609_000219-shm-dev15_aakvaerp_com-database.sql.gz
2025-06-09 16:00:48,170 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-09 16:00:48,326 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-09 16:00:48,327 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-09 16:00:48,362 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-09 16:00:48,368 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-09 16:01:38,418 INFO /home/<USER>/dev/dev/bin/bench --site site1 remove-from-installed-apps edu_tz
2025-06-09 16:01:53,569 INFO /home/<USER>/dev/dev/bin/bench --site health remove-from-installed-apps edu_tz
2025-06-09 16:02:03,319 INFO /home/<USER>/dev/dev/bin/bench --site health remove-from-installed-apps shm
2025-06-09 16:02:19,798 INFO /home/<USER>/dev/dev/bin/bench --site health remove-from-installed-apps vfd_tz
2025-06-09 16:02:30,981 INFO /home/<USER>/dev/dev/bin/bench --site health remove-from-installed-apps payware
2025-06-09 16:02:40,830 INFO /home/<USER>/dev/dev/bin/bench --site health remove-from-installed-apps non_profit
2025-06-09 16:03:07,507 INFO /home/<USER>/dev/dev/bin/bench --site health remove-from-installed-apps education
2025-06-09 16:03:54,869 INFO /home/<USER>/dev/dev/bin/bench --site health set-admin-password root
2025-06-09 16:04:02,093 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-09 16:04:02,249 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-09 16:04:02,252 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-09 16:04:02,281 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-09 16:04:02,293 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-09 16:05:37,242 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-09 16:12:49,194 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-09 16:12:49,350 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-09 16:12:49,352 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-09 16:12:49,364 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-09 16:12:49,364 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-09 16:19:09,976 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-09 16:29:49,625 INFO /home/<USER>/dev/dev/bin/bench --site health mariadb
2025-06-09 16:30:49,094 INFO /home/<USER>/dev/dev/bin/bench --site health console
2025-06-09 16:31:44,332 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-09 16:31:59,027 INFO /home/<USER>/dev/dev/bin/bench --site health console
2025-06-09 18:00:01,870 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-09 18:05:54,960 INFO /home/<USER>/dev/dev/bin/bench set-config -g server_script_enabled 1
2025-06-09 18:05:59,075 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-09 18:05:59,319 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-09 18:05:59,323 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-09 18:05:59,325 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-09 18:05:59,330 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-09 18:25:42,753 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-09 21:09:55,543 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-09 21:09:55,705 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-09 21:09:55,707 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-09 21:09:55,709 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-09 21:09:55,710 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-09 21:10:16,748 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-09 21:11:32,298 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-09 21:55:32,715 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-09 22:09:42,186 INFO /home/<USER>/dev/dev/bin/bench --site health console
2025-06-09 22:11:06,806 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 09:08:07,227 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-10 09:08:07,451 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-10 09:08:07,457 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-10 09:08:07,466 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-10 09:08:07,469 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-10 10:54:32,404 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-10 10:54:32,570 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-10 10:54:32,570 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-10 10:54:32,580 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-10 10:54:32,604 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-10 10:55:19,003 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 10:56:21,208 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 11:30:59,373 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 11:32:37,040 INFO /home/<USER>/dev/dev/bin/bench --site health clear-cache
2025-06-10 11:32:40,180 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 11:33:39,552 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 11:40:32,620 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 11:40:40,667 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 11:48:06,621 INFO /home/<USER>/dev/dev/bin/bench --site health migrate --skip-failing
2025-06-10 12:00:01,404 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-10 12:34:43,105 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-10 17:00:28,633 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-10 17:00:28,789 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-10 17:00:28,799 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-10 17:00:28,816 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-10 17:00:28,828 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-10 18:00:01,823 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-11 12:00:01,299 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-11 13:48:44,062 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-11 13:48:44,220 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-11 13:48:44,225 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-11 13:48:44,251 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-11 13:48:44,260 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-11 16:13:15,567 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-11 16:13:15,738 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-11 16:13:15,740 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-11 16:13:15,770 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-11 16:13:15,775 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-11 18:00:01,151 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-12 08:24:14,367 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-12 08:24:14,802 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-12 08:24:14,804 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-12 08:24:14,834 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-12 08:24:14,847 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-12 08:56:56,438 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 08:57:23,516 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 09:03:34,514 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 09:10:59,462 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 09:11:39,768 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 09:12:47,853 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 09:20:40,723 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 09:27:10,646 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 09:27:41,922 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 09:28:54,357 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-12 10:22:03,188 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-12 10:22:30,990 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 10:23:37,272 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 12:00:01,364 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-12 15:40:25,055 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-12 15:40:25,221 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-12 15:40:25,223 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-12 15:40:25,225 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-12 15:40:25,249 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-12 15:47:09,745 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 15:47:12,693 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 15:53:12,679 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 15:58:07,569 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 16:17:06,385 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 16:20:11,269 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 16:20:28,040 INFO /home/<USER>/dev/dev/bin/bench --site site1 execute propms.lease_invoice_schedule.make_lease_invoice_schedule
2025-06-12 16:21:00,348 INFO /home/<USER>/dev/dev/bin/bench --site site1 migrate
2025-06-12 18:00:01,919 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-12 18:27:31,523 INFO /home/<USER>/dev/dev/bin/bench new-site axessio
2025-06-12 18:29:02,187 INFO /home/<USER>/dev/dev/bin/bench --site axessio install-app csf_tz
2025-06-12 18:30:16,514 INFO /home/<USER>/dev/dev/bin/bench --site axessio install-app csf_tz
2025-06-12 18:31:29,079 INFO /home/<USER>/dev/dev/bin/bench --site axessio install-app propms
2025-06-12 18:33:48,547 INFO /home/<USER>/dev/dev/bin/bench --site axessio restore /home/<USER>/Downloads/20250612_021510-axessio_frappe_cloud-database.sql.gz
2025-06-12 18:40:17,837 INFO /home/<USER>/dev/dev/bin/bench --site axessio restore /home/<USER>/Downloads/20250612_021510-axessio_frappe_cloud-database.sql.gz
2025-06-12 22:44:42,498 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-12 22:44:42,686 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-12 22:44:42,688 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-12 22:44:42,693 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-12 22:44:42,698 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-12 22:45:57,398 INFO /home/<USER>/dev/dev/bin/bench --site axessio add-to-hosts
2025-06-12 22:47:13,169 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps print_designer
2025-06-12 22:47:32,271 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps banking
2025-06-12 22:47:47,034 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps banking
2025-06-12 22:47:53,533 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps eu_einvoice
2025-06-12 22:48:07,105 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps axessio_erpnext
2025-06-12 22:48:21,265 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps hr_addon
2025-06-12 22:48:33,253 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps active_users
2025-06-12 22:48:44,747 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps erpnext_datev
2025-06-12 22:48:57,319 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps erpnext_germany
2025-06-12 22:49:08,797 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps frappe_whatsapp
2025-06-12 22:49:22,772 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps drive
2025-06-12 22:49:33,970 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps pdf_on_submit
2025-06-12 22:49:48,965 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps erpnext_telegram_integration
2025-06-12 22:49:59,170 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps wiki
2025-06-12 22:50:10,581 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps crm
2025-06-12 22:50:21,655 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps insights
2025-06-12 22:50:32,378 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps helpdesk
2025-06-12 22:50:46,321 INFO /home/<USER>/dev/dev/bin/bench --site axessio remove-from-installed-apps builder
2025-06-12 22:51:10,049 INFO /home/<USER>/dev/dev/bin/bench --site axessio migrate
2025-06-12 23:57:01,152 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-12 23:57:01,387 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-12 23:57:01,392 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-12 23:57:01,404 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-12 23:57:01,426 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-13 00:00:02,105 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-13 08:54:50,415 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-13 08:54:50,579 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-13 08:54:50,585 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-13 08:54:50,604 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-13 08:54:50,614 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-13 12:00:02,157 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-13 14:28:53,713 INFO /home/<USER>/dev/dev/bin/bench --site site1 console
2025-06-13 18:00:02,111 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-15 12:00:01,344 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-15 15:54:47,387 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-15 15:54:47,548 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-15 15:54:47,557 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-15 15:54:47,590 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-15 15:54:47,594 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-15 18:00:01,929 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-15 21:07:33,492 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-15 21:07:34,093 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-15 21:07:34,109 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-15 21:07:34,115 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-15 21:07:34,121 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-15 22:04:58,269 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-16 08:42:43,937 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-16 08:42:44,502 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-16 08:42:44,511 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-16 08:42:44,514 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-16 08:42:44,514 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-16 09:59:25,397 INFO /home/<USER>/dev/dev/bin/bench get-app --branch v15_nhif_test https://github.com/Aakvatech-Limited/HMS_TZ.git
2025-06-16 09:59:25,415 LOG Getting HMS_TZ
2025-06-16 09:59:25,415 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/HMS_TZ.git --branch v15_nhif_test --depth 1 --origin upstream
2025-06-16 09:59:29,331 LOG Installing hms_tz
2025-06-16 09:59:29,336 DEBUG cd /home/<USER>/dev/frappe-bench && /home/<USER>/dev/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/frappe-bench/apps/hms_tz 
2025-06-16 09:59:44,629 DEBUG cd /home/<USER>/dev/frappe-bench/apps/hms_tz && yarn install --check-files
2025-06-16 09:59:52,480 DEBUG bench build --app hms_tz
2025-06-16 09:59:52,960 INFO /home/<USER>/dev/dev/bin/bench build --app hms_tz
2025-06-16 10:00:00,483 INFO A newer version of bench is available: 5.25.1 → 5.25.4
2025-06-16 10:00:08,157 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-16 10:00:08,659 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-16 10:00:08,675 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-16 10:00:08,703 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-16 10:00:08,704 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-16 10:00:27,438 INFO /home/<USER>/dev/dev/bin/bench --site health migrate
2025-06-16 10:54:44,539 INFO /home/<USER>/dev/dev/bin/bench --site health console
2025-06-16 10:55:41,673 INFO /home/<USER>/dev/dev/bin/bench --site health console
2025-06-16 12:00:01,808 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-16 18:00:01,685 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-17 12:00:01,966 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-17 13:42:34,449 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-17 13:42:34,993 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-17 13:42:35,007 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-17 13:42:35,048 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-17 13:42:35,054 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-17 13:45:16,969 INFO /home/<USER>/dev/dev/bin/bench use health
2025-06-17 13:45:58,380 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-17 13:45:58,925 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-17 13:45:58,980 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-17 13:45:58,981 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-17 13:45:58,988 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-17 18:00:01,527 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-18 09:21:35,930 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-18 09:21:36,558 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-18 09:21:36,568 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-18 09:21:36,635 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-18 09:21:36,639 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-18 12:00:01,867 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-18 13:07:21,372 INFO /home/<USER>/dev/dev/bin/bench use site1
2025-06-18 13:07:28,172 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-18 13:07:28,645 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-18 13:07:28,701 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-18 13:07:28,728 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-18 13:07:28,733 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-18 16:25:06,960 INFO /home/<USER>/dev/dev/bin/bench new-site viva
2025-06-18 16:26:33,848 INFO /home/<USER>/dev/dev/bin/bench --site viva install-app csf_tz
2025-06-18 16:29:12,553 INFO /home/<USER>/dev/dev/bin/bench --site viva install-app csf_tz
2025-06-18 16:32:29,203 INFO /home/<USER>/dev/dev/bin/bench --site viva install-app propms
2025-06-18 16:49:00,543 INFO /home/<USER>/dev/dev/bin/bench --site viva restore /home/<USER>/Downloads/20250617_020010-viva14-av_frappe_cloud-database.sql.gz
2025-06-18 16:52:06,555 INFO /home/<USER>/dev/dev/bin/bench --site viva restore /home/<USER>/Downloads/20250617_020010-viva14-av_frappe_cloud-database.sql.gz
2025-06-18 16:52:32,479 INFO /home/<USER>/dev/dev/bin/bench --site viva restore /home/<USER>/Downloads/20250617_020010-viva14-av_frappe_cloud-database.sql.gz
2025-06-18 18:00:01,786 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-18 22:18:15,999 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-18 22:18:16,448 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-18 22:18:16,450 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-18 22:18:16,481 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-18 22:18:16,508 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-19 10:09:52,355 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-19 10:09:53,029 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-19 10:09:53,030 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-19 10:09:53,033 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-19 10:09:53,081 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-19 11:16:34,050 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-19 11:16:34,523 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-19 11:16:34,528 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-19 11:16:34,552 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-19 11:16:34,574 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-19 12:00:02,174 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-06-19 12:24:25,844 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-19 12:24:26,402 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-19 12:24:26,405 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-19 12:24:26,432 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-19 12:24:26,435 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-19 15:19:07,389 INFO /home/<USER>/dev/dev/bin/bench start
2025-06-19 15:19:07,544 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-06-19 15:19:07,546 INFO /home/<USER>/dev/dev/bin/bench worker
2025-06-19 15:19:07,586 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-06-19 15:19:07,593 INFO /home/<USER>/dev/dev/bin/bench watch
2025-06-19 18:00:01,908 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
