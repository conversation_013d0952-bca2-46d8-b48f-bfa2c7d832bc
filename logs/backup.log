set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250604_180001-site1-database.sql.gz

Backup Summary for site1 at 2025-06-04 18:00:02.612526
Config  : ./site1/private/backups/20250604_180001-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250604_180001-site1-database.sql.gz         1.1MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250605_120002-health-database.sql.gz

Backup Summary for health at 2025-06-05 12:00:03.753204
Config  : ./health/private/backups/20250605_120002-health-site_config_backup.json 158.0B
Database: ./health/private/backups/20250605_120002-health-database.sql.gz         1.3MiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250604_180001-site1-site_config_backup.json is recent
File ./site1/private/backups/20250604_180001-site1-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250605_120003-site1-database.sql.gz

Backup Summary for site1 at 2025-06-05 12:00:04.715391
Config  : ./site1/private/backups/20250605_120003-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250605_120003-site1-database.sql.gz         1.1MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250605_180001-health-database.sql.gz

Backup Summary for health at 2025-06-05 18:00:03.930060
Config  : ./health/private/backups/20250605_180001-health-site_config_backup.json 158.0B
Database: ./health/private/backups/20250605_180001-health-database.sql.gz         1.3MiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250605_120003-site1-site_config_backup.json is recent
File ./site1/private/backups/20250604_180001-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250604_180001-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250605_120003-site1-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250605_180003-site1-database.sql.gz

Backup Summary for site1 at 2025-06-05 18:00:05.594882
Config  : ./site1/private/backups/20250605_180003-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250605_180003-site1-database.sql.gz         1.1MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250606_120001-health-database.sql.gz

Backup Summary for health at 2025-06-06 12:00:03.914652
Config  : ./health/private/backups/20250606_120001-health-site_config_backup.json 158.0B
Database: ./health/private/backups/20250606_120001-health-database.sql.gz         1.3MiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250605_180003-site1-database.sql.gz is recent
File ./site1/private/backups/20250605_180003-site1-site_config_backup.json is recent
File ./site1/private/backups/20250605_120003-site1-site_config_backup.json is recent
File ./site1/private/backups/20250605_120003-site1-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250606_120003-site1-database.sql.gz

Backup Summary for site1 at 2025-06-06 12:00:04.927357
Config  : ./site1/private/backups/20250606_120003-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250606_120003-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250606_180002-health-database.sql.gz

Backup Summary for health at 2025-06-06 18:00:03.620441
Config  : ./health/private/backups/20250606_180002-health-site_config_backup.json 158.0B
Database: ./health/private/backups/20250606_180002-health-database.sql.gz         1.4MiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250605_180003-site1-database.sql.gz is recent
File ./site1/private/backups/20250605_180003-site1-site_config_backup.json is recent
File ./site1/private/backups/20250605_120003-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250606_120003-site1-site_config_backup.json is recent
File ./site1/private/backups/20250606_120003-site1-database.sql.gz is recent
File ./site1/private/backups/20250605_120003-site1-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250606_180003-site1-database.sql.gz

Backup Summary for site1 at 2025-06-06 18:00:04.777530
Config  : ./site1/private/backups/20250606_180003-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250606_180003-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250607_180012-health-database.sql.gz

Backup Summary for health at 2025-06-07 18:00:17.268937
Config  : ./health/private/backups/20250607_180012-health-site_config_backup.json 158.0B
Database: ./health/private/backups/20250607_180012-health-database.sql.gz         1.4MiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250605_180003-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250605_180003-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250606_180003-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250606_120003-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250606_180003-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250606_120003-site1-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250607_180017-site1-database.sql.gz

Backup Summary for site1 at 2025-06-07 18:00:21.037201
Config  : ./site1/private/backups/20250607_180017-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250607_180017-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250609_120002-health-database.sql.gz

Backup Summary for health at 2025-06-09 12:00:04.087873
Config  : ./health/private/backups/20250609_120002-health-site_config_backup.json 158.0B
Database: ./health/private/backups/20250609_120002-health-database.sql.gz         1.4MiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250607_180017-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250607_180017-site1-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250609_120004-site1-database.sql.gz

Backup Summary for site1 at 2025-06-09 12:00:05.068604
Config  : ./site1/private/backups/20250609_120004-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250609_120004-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250609_180002-health-database.sql.gz

Backup Summary for health at 2025-06-09 18:09:45.769311
Config  : ./health/private/backups/20250609_180002-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250609_180002-health-database.sql.gz         6.4GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250609_120004-site1-site_config_backup.json is recent
File ./site1/private/backups/20250609_120004-site1-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250609_180945-site1-database.sql.gz

Backup Summary for site1 at 2025-06-09 18:09:47.489496
Config  : ./site1/private/backups/20250609_180945-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250609_180945-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250610_120001-health-database.sql.gz

Backup Summary for health at 2025-06-10 12:11:30.159570
Config  : ./health/private/backups/20250610_120001-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250610_120001-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250609_180945-site1-site_config_backup.json is recent
File ./site1/private/backups/20250609_120004-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250609_180945-site1-database.sql.gz is recent
File ./site1/private/backups/20250609_120004-site1-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250610_121130-site1-database.sql.gz

Backup Summary for site1 at 2025-06-10 12:11:32.239424
Config  : ./site1/private/backups/20250610_121130-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250610_121130-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250610_180002-health-database.sql.gz

Backup Summary for health at 2025-06-10 18:09:50.099637
Config  : ./health/private/backups/20250610_180002-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250610_180002-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250610_121130-site1-database.sql.gz is recent
File ./site1/private/backups/20250609_180945-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250610_121130-site1-site_config_backup.json is recent
File ./site1/private/backups/20250609_180945-site1-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250610_180950-site1-database.sql.gz

Backup Summary for site1 at 2025-06-10 18:09:52.331412
Config  : ./site1/private/backups/20250610_180950-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250610_180950-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250611_120001-health-database.sql.gz

Backup Summary for health at 2025-06-11 12:10:17.607659
Config  : ./health/private/backups/20250611_120001-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250611_120001-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250610_121130-site1-database.sql.gz is recent
File ./site1/private/backups/20250610_180950-site1-database.sql.gz is recent
File ./site1/private/backups/20250610_180950-site1-site_config_backup.json is recent
File ./site1/private/backups/20250610_121130-site1-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250611_121017-site1-database.sql.gz

Backup Summary for site1 at 2025-06-11 12:10:19.765447
Config  : ./site1/private/backups/20250611_121017-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250611_121017-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250611_180001-health-database.sql.gz

Backup Summary for health at 2025-06-11 18:10:06.209719
Config  : ./health/private/backups/20250611_180001-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250611_180001-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250610_121130-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250610_180950-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250611_121017-site1-database.sql.gz is recent
File ./site1/private/backups/20250610_180950-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250610_121130-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250611_121017-site1-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250611_181006-site1-database.sql.gz

Backup Summary for site1 at 2025-06-11 18:10:07.587750
Config  : ./site1/private/backups/20250611_181006-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250611_181006-site1-database.sql.gz         1.2MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250612_120001-health-database.sql.gz

Backup Summary for health at 2025-06-12 12:10:08.594164
Config  : ./health/private/backups/20250612_120001-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250612_120001-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250611_181006-site1-database.sql.gz is recent
File ./site1/private/backups/20250611_121017-site1-database.sql.gz is recent
File ./site1/private/backups/20250611_121017-site1-site_config_backup.json is recent
File ./site1/private/backups/20250611_181006-site1-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250612_121008-site1-database.sql.gz

Backup Summary for site1 at 2025-06-12 12:10:10.159417
Config  : ./site1/private/backups/20250612_121008-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250612_121008-site1-database.sql.gz         1.3MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250612_180002-health-database.sql.gz

Backup Summary for health at 2025-06-12 18:09:38.179181
Config  : ./health/private/backups/20250612_180002-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250612_180002-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250611_181006-site1-database.sql.gz is recent
File ./site1/private/backups/20250611_121017-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250612_121008-site1-database.sql.gz is recent
File ./site1/private/backups/20250611_121017-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250612_121008-site1-site_config_backup.json is recent
File ./site1/private/backups/20250611_181006-site1-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250612_180938-site1-database.sql.gz

Backup Summary for site1 at 2025-06-12 18:09:39.633646
Config  : ./site1/private/backups/20250612_180938-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250612_180938-site1-database.sql.gz         1.3MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250612_230002-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-12 23:00:09.173236
Config  : ./axessio/private/backups/20250612_230002-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250612_230002-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250612_180002-health-database.sql.gz is recent
File ./health/private/backups/20250612_120001-health-database.sql.gz is recent
File ./health/private/backups/20250612_120001-health-site_config_backup.json is recent
File ./health/private/backups/20250612_180002-health-site_config_backup.json is recent
File ./health/private/backups/20250611_180001-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250611_180001-health-site_config_backup.json is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250613_000009-health-database.sql.gz

Backup Summary for health at 2025-06-13 00:12:38.372375
Config  : ./health/private/backups/20250613_000009-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250613_000009-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250611_181006-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250612_180938-site1-site_config_backup.json is recent
File ./site1/private/backups/20250612_121008-site1-database.sql.gz is recent
File ./site1/private/backups/20250612_121008-site1-site_config_backup.json is recent
File ./site1/private/backups/20250611_181006-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250612_180938-site1-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250613_001238-site1-database.sql.gz

Backup Summary for site1 at 2025-06-13 00:12:40.030183
Config  : ./site1/private/backups/20250613_001238-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250613_001238-site1-database.sql.gz         1.3MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250613_110002-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-13 11:00:07.213430
Config  : ./axessio/private/backups/20250613_110002-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250613_110002-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250612_180002-health-database.sql.gz is recent
File ./health/private/backups/20250612_120001-health-database.sql.gz is recent
File ./health/private/backups/20250613_000009-health-database.sql.gz is recent
File ./health/private/backups/20250612_120001-health-site_config_backup.json is recent
File ./health/private/backups/20250613_000009-health-site_config_backup.json is recent
File ./health/private/backups/20250612_180002-health-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250613_120007-health-database.sql.gz

Backup Summary for health at 2025-06-13 12:11:06.722084
Config  : ./health/private/backups/20250613_120007-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250613_120007-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250612_180938-site1-site_config_backup.json is recent
File ./site1/private/backups/20250612_121008-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250612_121008-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250613_001238-site1-database.sql.gz is recent
File ./site1/private/backups/20250613_001238-site1-site_config_backup.json is recent
File ./site1/private/backups/20250612_180938-site1-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250613_121106-site1-database.sql.gz

Backup Summary for site1 at 2025-06-13 12:11:08.390163
Config  : ./site1/private/backups/20250613_121106-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250613_121106-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250613_170002-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-13 17:00:06.220587
Config  : ./axessio/private/backups/20250613_170002-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250613_170002-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250612_180002-health-database.sql.gz is recent
File ./health/private/backups/20250612_120001-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250613_000009-health-database.sql.gz is recent
File ./health/private/backups/20250613_120007-health-database.sql.gz is recent
File ./health/private/backups/20250612_120001-health-site_config_backup.json is older than 24 hours
File ./health/private/backups/20250613_000009-health-site_config_backup.json is recent
File ./health/private/backups/20250612_180002-health-site_config_backup.json is recent
File ./health/private/backups/20250613_120007-health-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250613_180006-health-database.sql.gz

b'mariadb-dump: Error 2013: Lost connection to server during query when dumping table `tabPatient Encounter` at row: 437228\n'
Backup failed for Site health. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['axessio', 'health', 'site1'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x769e4c1f7740>
      exit_code = 0
      rollback_callback = None
      site = 'health'
      odb = <frappe.utils.backups.BackupGenerator object at 0x769e4c3d6720>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x769e4b027ca0>
  File "apps/frappe/frappe/utils/backups.py", line 626, in new_backup
    odb.get_backup(older_than, ignore_files, force=force)
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x769e4b027ca0>
      odb = <frappe.utils.backups.BackupGenerator object at 0x769e4e5f7bf0>
  File "apps/frappe/frappe/utils/backups.py", line 195, in get_backup
    self.delete_if_step_fails(self.take_dump, self.backup_path_db)
      self = <frappe.utils.backups.BackupGenerator object at 0x769e4e5f7bf0>
      older_than = 6
      ignore_files = True
      force = True
      last_db = False
      last_file = False
      last_private_file = False
      site_config_backup_path = False
  File "apps/frappe/frappe/utils/backups.py", line 520, in delete_if_step_fails
    raise e
      self = <frappe.utils.backups.BackupGenerator object at 0x769e4e5f7bf0>
      step = <bound method BackupGenerator.take_dump of <frappe.utils.backups.BackupGenerator object at 0x769e4e5f7bf0>>
      paths = ('./health/private/backups/20250613_180006-health-database.sql.gz',)
      path = './health/private/backups/20250613_180006-health-database.sql.gz'
  File "apps/frappe/frappe/utils/backups.py", line 515, in delete_if_step_fails
    step()
      self = <frappe.utils.backups.BackupGenerator object at 0x769e4e5f7bf0>
      step = <bound method BackupGenerator.take_dump of <frappe.utils.backups.BackupGenerator object at 0x769e4e5f7bf0>>
      paths = ('./health/private/backups/20250613_180006-health-database.sql.gz',)
      path = './health/private/backups/20250613_180006-health-database.sql.gz'
  File "apps/frappe/frappe/utils/backups.py", line 468, in take_dump
    frappe.utils.execute_in_shell(command, low_priority=True, check_exit_code=True)
      self = <frappe.utils.backups.BackupGenerator object at 0x769e4e5f7bf0>
      shlex = <module 'shlex' from '/usr/lib/python3.12/shlex.py'>
      frappe = <module 'frappe' from 'apps/frappe/frappe/__init__.py'>
      get_app_branch = <function get_app_branch at 0x769e4c2cef20>
      gzip_exc = '/usr/bin/gzip'
      database_header_content = ['begin frappe metadata', '[frappe]', 'version = 15.69.3', 'branch = version-15', 'end frappe metadata', '']
      generated_header = '-- begin frappe metadata\n-- [frappe]\n-- version = 15.69.3\n-- branch = version-15\n-- end frappe metadata\n-- \n'
      f = <_io.TextIOWrapper name='./health/private/backups/20250613_180006-health-database.sql.gz' encoding='UTF-8'>
      cmd = ['/usr/bin/mariadb-dump', '--user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=sSVb3wiF6q6VGL5S --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f']
      extra = []
      get_command = <function get_command at 0x769e4c4ea020>
      bin = '/usr/bin/mariadb-dump'
      args = ['--user=_5c0a108e8be5ba0f', '--host=127.0.0.1', '--port=3306', '--password=sSVb3wiF6q6VGL5S', '--single-transaction', '--quick', '--lock-tables=false', '_5c0a108e8be5ba0f']
      bin_name = 'mariadb-dump'
      command = 'set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=sSVb3wiF6q6VGL5S --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250613_180006-health-database.sql.gz'
  File "apps/frappe/frappe/utils/__init__.py", line 484, in execute_in_shell
    raise frappe.CommandFailedError(
      cmd = 'set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=sSVb3wiF6q6VGL5S --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250613_180006-health-database.sql.gz'
      verbose = False
      low_priority = True
      check_exit_code = True
      shlex = <module 'shlex' from '/usr/lib/python3.12/shlex.py'>
      tempfile = <module 'tempfile' from '/usr/lib/python3.12/tempfile.py'>
      Popen = <class 'subprocess.Popen'>
      stdout = <_io.BufferedRandom name=8>
      stderr = <_io.BufferedRandom name=9>
      kwargs = <exception while printing> Traceback (most recent call last):
          File "env/lib/python3.12/site-packages/traceback_with_variables/core.py", line 222, in _to_cropped_str
            raw = print_(obj)
                  ^^^^^^^^^^^
          File "apps/frappe/frappe/utils/__init__.py", line 326, in dict_printer
            v = deepcopy(v)
                ^^^^^^^^^^^
          File "/usr/lib/python3.12/copy.py", line 136, in deepcopy
            y = copier(x, memo)
                ^^^^^^^^^^^^^^^
          File "/usr/lib/python3.12/copy.py", line 221, in _deepcopy_dict
            y[deepcopy(key, memo)] = deepcopy(value, memo)
                                     ^^^^^^^^^^^^^^^^^^^^^
          File "/usr/lib/python3.12/copy.py", line 151, in deepcopy
            rv = reductor(4)
                 ^^^^^^^^^^^
        TypeError: cannot pickle 'BufferedRandom' instances
        
      p = <Popen: returncode: 3 args: 'set -o pipefail; /usr/bin/mariadb-dump --user=_...>
      exit_code = 3
      out = b''
      err = b'mariadb-dump: Error 2013: Lost connection to server during query when dumping table `tabPatient Encounter` at row: 437228\n'
      failed = 3
frappe.exceptions.CommandFailedError: Command failed
File ./health/private/backups/20250612_180002-health-database.sql.gz is recent
File ./health/private/backups/20250613_000009-health-database.sql.gz is recent
File ./health/private/backups/20250613_120007-health-database.sql.gz is recent
File ./health/private/backups/20250613_000009-health-site_config_backup.json is recent
File ./health/private/backups/20250612_180002-health-site_config_backup.json is recent
File ./health/private/backups/20250613_120007-health-site_config_backup.json is recent
Backup failed for Site site1. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['axessio', 'health', 'site1'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x769e4c1f7740>
      exit_code = 1
      rollback_callback = None
      site = 'site1'
      odb = <frappe.utils.backups.BackupGenerator object at 0x769e4c3d6720>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x769e4c559900>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x769e4c559900>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x769e4c1e3e30>
      db_name = '_5c0a108e8be5ba0f'
      user = '_5c0a108e8be5ba0f'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x769e4c559900>
      site = 'health'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x769e4c1e3e30>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x769e4b027cb0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x769e4b027e30>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_5c0a108e8be5ba0f'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x769e4b03c5e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x769e4b03c680>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x769e4b027cb0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_5c0a108e8be5ba0f'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x769e4b027cb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x769e4b027cb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x769e4b027cb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x769e4b027cb0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x769e4b0469f0>
      user = '_5c0a108e8be5ba0f'
      password = ********
      host = '127.0.0.1'
      database = '_5c0a108e8be5ba0f'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x769e4b1c8540>, <class 'int'>: <function escape_int at 0x769e4b1c85e0>, <class 'float'>: <function escape_float at 0x769e4b1c8680>, <class 'str'>: <function escape_str at 0x769e4b1c8900>, <class 'bytes'>: <function escape_bytes at 0x769e4b1c8860>, <class 'tuple'>: <function escape_sequence at 0x769e4b1c8400>, <class 'list'>: <function escape_sequence at 0x769e4b1c8400>, <class 'set'>: <function escape_sequence at 0x769e4b1c8400>, <class 'frozenset'>: <function escape_sequence at 0x769e4b1c8400>, <class 'dict'>: <function escape_dict at 0x769e4b1c8360>, <class 'NoneType'>: <function escape_None at 0x769e4b1c89a0>, <class 'datetime.date'>: <function escape_date at 0x769e4b1c8c20>, <class 'datetime.datetime'>: <function escape_datetime at 0x769e4b1c8b80>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x769e4b1c8a40>, <class 'datetime.time'>: <function escape_time at 0x769e4b1c8ae0>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
      self = <pymysql.connections.Connection object at 0x769e4b0469f0>
      sock = None
      kwargs = {}
      exc = OperationalError(2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250615_110001-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-15 11:00:05.873650
Config  : ./axessio/private/backups/20250615_110001-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250615_110001-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250612_180002-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250613_000009-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250613_120007-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250613_000009-health-site_config_backup.json is older than 24 hours
File ./health/private/backups/20250612_180002-health-site_config_backup.json is older than 24 hours
File ./health/private/backups/20250613_120007-health-site_config_backup.json is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250615_120005-health-database.sql.gz

Backup Summary for health at 2025-06-15 12:08:44.459453
Config  : ./health/private/backups/20250615_120005-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250615_120005-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250612_180938-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250613_121106-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250613_121106-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250613_001238-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250613_001238-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250612_180938-site1-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250615_120844-site1-database.sql.gz

Backup Summary for site1 at 2025-06-15 12:08:46.288534
Config  : ./site1/private/backups/20250615_120844-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250615_120844-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250615_170002-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-15 17:00:06.667741
Config  : ./axessio/private/backups/20250615_170002-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250615_170002-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250615_120005-health-site_config_backup.json is recent
File ./health/private/backups/20250615_120005-health-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250615_180006-health-database.sql.gz

Backup Summary for health at 2025-06-15 18:09:26.761402
Config  : ./health/private/backups/20250615_180006-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250615_180006-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250615_120844-site1-database.sql.gz is recent
File ./site1/private/backups/20250615_120844-site1-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250615_180926-site1-database.sql.gz

Backup Summary for site1 at 2025-06-15 18:09:28.671058
Config  : ./site1/private/backups/20250615_180926-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250615_180926-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250616_110002-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-16 11:00:16.341797
Config  : ./axessio/private/backups/20250616_110002-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250616_110002-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250615_120005-health-site_config_backup.json is recent
File ./health/private/backups/20250615_180006-health-database.sql.gz is recent
File ./health/private/backups/20250615_180006-health-site_config_backup.json is recent
File ./health/private/backups/20250615_120005-health-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250616_120016-health-database.sql.gz

Backup Summary for health at 2025-06-16 12:24:46.366391
Config  : ./health/private/backups/20250616_120016-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250616_120016-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250615_120844-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250615_120844-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250615_180926-site1-site_config_backup.json is recent
File ./site1/private/backups/20250615_180926-site1-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250616_122446-site1-database.sql.gz

Backup Summary for site1 at 2025-06-16 12:24:50.917407
Config  : ./site1/private/backups/20250616_122446-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250616_122446-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250616_170003-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-16 17:00:14.406992
Config  : ./axessio/private/backups/20250616_170003-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250616_170003-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250615_120005-health-site_config_backup.json is older than 24 hours
File ./health/private/backups/20250616_120016-health-site_config_backup.json is recent
File ./health/private/backups/20250615_180006-health-database.sql.gz is recent
File ./health/private/backups/20250615_180006-health-site_config_backup.json is recent
File ./health/private/backups/20250616_120016-health-database.sql.gz is recent
File ./health/private/backups/20250615_120005-health-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250616_180014-health-database.sql.gz

Backup Summary for health at 2025-06-16 18:22:20.295727
Config  : ./health/private/backups/20250616_180014-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250616_180014-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250616_122446-site1-site_config_backup.json is recent
File ./site1/private/backups/20250616_122446-site1-database.sql.gz is recent
File ./site1/private/backups/20250615_180926-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250615_180926-site1-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250616_182220-site1-database.sql.gz

Backup Summary for site1 at 2025-06-16 18:22:27.099331
Config  : ./site1/private/backups/20250616_182220-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250616_182220-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250617_110003-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-17 11:00:15.158481
Config  : ./axessio/private/backups/20250617_110003-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250617_110003-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250616_180014-health-database.sql.gz is recent
File ./health/private/backups/20250616_180014-health-site_config_backup.json is recent
File ./health/private/backups/20250616_120016-health-site_config_backup.json is recent
File ./health/private/backups/20250615_180006-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250615_180006-health-site_config_backup.json is older than 24 hours
File ./health/private/backups/20250616_120016-health-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250617_120015-health-database.sql.gz

Backup Summary for health at 2025-06-17 12:23:49.616367
Config  : ./health/private/backups/20250617_120015-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250617_120015-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250616_122446-site1-site_config_backup.json is recent
File ./site1/private/backups/20250616_122446-site1-database.sql.gz is recent
File ./site1/private/backups/20250616_182220-site1-database.sql.gz is recent
File ./site1/private/backups/20250616_182220-site1-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250617_122349-site1-database.sql.gz

Backup Summary for site1 at 2025-06-17 12:23:55.091934
Config  : ./site1/private/backups/20250617_122349-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250617_122349-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250617_170002-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-17 17:00:13.647194
Config  : ./axessio/private/backups/20250617_170002-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250617_170002-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250616_180014-health-database.sql.gz is recent
File ./health/private/backups/20250616_180014-health-site_config_backup.json is recent
File ./health/private/backups/20250616_120016-health-site_config_backup.json is older than 24 hours
File ./health/private/backups/20250617_120015-health-database.sql.gz is recent
File ./health/private/backups/20250616_120016-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250617_120015-health-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250617_180013-health-database.sql.gz

Backup Summary for health at 2025-06-17 18:22:17.801822
Config  : ./health/private/backups/20250617_180013-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250617_180013-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250617_122349-site1-database.sql.gz is recent
File ./site1/private/backups/20250616_122446-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250617_122349-site1-site_config_backup.json is recent
File ./site1/private/backups/20250616_122446-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250616_182220-site1-database.sql.gz is recent
File ./site1/private/backups/20250616_182220-site1-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250617_182217-site1-database.sql.gz

Backup Summary for site1 at 2025-06-17 18:22:22.313012
Config  : ./site1/private/backups/20250617_182217-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250617_182217-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250618_110002-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-18 11:00:14.836079
Config  : ./axessio/private/backups/20250618_110002-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250618_110002-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250616_180014-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250616_180014-health-site_config_backup.json is older than 24 hours
File ./health/private/backups/20250617_120015-health-database.sql.gz is recent
File ./health/private/backups/20250617_120015-health-site_config_backup.json is recent
File ./health/private/backups/20250617_180013-health-database.sql.gz is recent
File ./health/private/backups/20250617_180013-health-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250618_120014-health-database.sql.gz

Backup Summary for health at 2025-06-18 12:30:34.344012
Config  : ./health/private/backups/20250618_120014-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250618_120014-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250617_122349-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250617_122349-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250616_182220-site1-database.sql.gz is older than 24 hours
File ./site1/private/backups/20250616_182220-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250617_182217-site1-site_config_backup.json is recent
File ./site1/private/backups/20250617_182217-site1-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250618_123034-site1-database.sql.gz

Backup Summary for site1 at 2025-06-18 12:30:39.593140
Config  : ./site1/private/backups/20250618_123034-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250618_123034-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250618_170003-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-18 17:00:15.614967
Config  : ./axessio/private/backups/20250618_170003-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250618_170003-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250618_120014-health-database.sql.gz is recent
File ./health/private/backups/20250617_120015-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250617_120015-health-site_config_backup.json is older than 24 hours
File ./health/private/backups/20250618_120014-health-site_config_backup.json is recent
File ./health/private/backups/20250617_180013-health-database.sql.gz is recent
File ./health/private/backups/20250617_180013-health-site_config_backup.json is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250618_180015-health-database.sql.gz

b'mariadb-dump: Error 2013: Lost connection to server during query when dumping table `tabPatient Encounter` at row: 183756\n'
Backup failed for Site health. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['axessio', 'health', 'site1', 'viva'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7ea842bf3740>
      exit_code = 0
      rollback_callback = None
      site = 'health'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7ea841a9e540>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ea84194c4f0>
  File "apps/frappe/frappe/utils/backups.py", line 626, in new_backup
    odb.get_backup(older_than, ignore_files, force=force)
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ea84194c4f0>
      odb = <frappe.utils.backups.BackupGenerator object at 0x7ea8434eef60>
  File "apps/frappe/frappe/utils/backups.py", line 195, in get_backup
    self.delete_if_step_fails(self.take_dump, self.backup_path_db)
      self = <frappe.utils.backups.BackupGenerator object at 0x7ea8434eef60>
      older_than = 6
      ignore_files = True
      force = True
      last_db = False
      last_file = False
      last_private_file = False
      site_config_backup_path = False
  File "apps/frappe/frappe/utils/backups.py", line 520, in delete_if_step_fails
    raise e
      self = <frappe.utils.backups.BackupGenerator object at 0x7ea8434eef60>
      step = <bound method BackupGenerator.take_dump of <frappe.utils.backups.BackupGenerator object at 0x7ea8434eef60>>
      paths = ('./health/private/backups/20250618_180015-health-database.sql.gz',)
      path = './health/private/backups/20250618_180015-health-database.sql.gz'
  File "apps/frappe/frappe/utils/backups.py", line 515, in delete_if_step_fails
    step()
      self = <frappe.utils.backups.BackupGenerator object at 0x7ea8434eef60>
      step = <bound method BackupGenerator.take_dump of <frappe.utils.backups.BackupGenerator object at 0x7ea8434eef60>>
      paths = ('./health/private/backups/20250618_180015-health-database.sql.gz',)
      path = './health/private/backups/20250618_180015-health-database.sql.gz'
  File "apps/frappe/frappe/utils/backups.py", line 468, in take_dump
    frappe.utils.execute_in_shell(command, low_priority=True, check_exit_code=True)
      self = <frappe.utils.backups.BackupGenerator object at 0x7ea8434eef60>
      shlex = <module 'shlex' from '/usr/lib/python3.12/shlex.py'>
      frappe = <module 'frappe' from 'apps/frappe/frappe/__init__.py'>
      get_app_branch = <function get_app_branch at 0x7ea842ccaf20>
      gzip_exc = '/usr/bin/gzip'
      database_header_content = ['begin frappe metadata', '[frappe]', 'version = 15.69.3', 'branch = version-15', 'end frappe metadata', '']
      generated_header = '-- begin frappe metadata\n-- [frappe]\n-- version = 15.69.3\n-- branch = version-15\n-- end frappe metadata\n-- \n'
      f = <_io.TextIOWrapper name='./health/private/backups/20250618_180015-health-database.sql.gz' encoding='UTF-8'>
      cmd = ['/usr/bin/mariadb-dump', '--user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=sSVb3wiF6q6VGL5S --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f']
      extra = []
      get_command = <function get_command at 0x7ea842eea020>
      bin = '/usr/bin/mariadb-dump'
      args = ['--user=_5c0a108e8be5ba0f', '--host=127.0.0.1', '--port=3306', '--password=sSVb3wiF6q6VGL5S', '--single-transaction', '--quick', '--lock-tables=false', '_5c0a108e8be5ba0f']
      bin_name = 'mariadb-dump'
      command = 'set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=sSVb3wiF6q6VGL5S --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250618_180015-health-database.sql.gz'
  File "apps/frappe/frappe/utils/__init__.py", line 484, in execute_in_shell
    raise frappe.CommandFailedError(
      cmd = 'set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=sSVb3wiF6q6VGL5S --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250618_180015-health-database.sql.gz'
      verbose = False
      low_priority = True
      check_exit_code = True
      shlex = <module 'shlex' from '/usr/lib/python3.12/shlex.py'>
      tempfile = <module 'tempfile' from '/usr/lib/python3.12/tempfile.py'>
      Popen = <class 'subprocess.Popen'>
      stdout = <_io.BufferedRandom name=8>
      stderr = <_io.BufferedRandom name=9>
      kwargs = <exception while printing> Traceback (most recent call last):
          File "env/lib/python3.12/site-packages/traceback_with_variables/core.py", line 222, in _to_cropped_str
            raw = print_(obj)
                  ^^^^^^^^^^^
          File "apps/frappe/frappe/utils/__init__.py", line 326, in dict_printer
            v = deepcopy(v)
                ^^^^^^^^^^^
          File "/usr/lib/python3.12/copy.py", line 136, in deepcopy
            y = copier(x, memo)
                ^^^^^^^^^^^^^^^
          File "/usr/lib/python3.12/copy.py", line 221, in _deepcopy_dict
            y[deepcopy(key, memo)] = deepcopy(value, memo)
                                     ^^^^^^^^^^^^^^^^^^^^^
          File "/usr/lib/python3.12/copy.py", line 151, in deepcopy
            rv = reductor(4)
                 ^^^^^^^^^^^
        TypeError: cannot pickle 'BufferedRandom' instances
        
      p = <Popen: returncode: 3 args: 'set -o pipefail; /usr/bin/mariadb-dump --user=_...>
      exit_code = 3
      out = b''
      err = b'mariadb-dump: Error 2013: Lost connection to server during query when dumping table `tabPatient Encounter` at row: 183756\n'
      failed = 3
frappe.exceptions.CommandFailedError: Command failed
File ./health/private/backups/20250618_120014-health-database.sql.gz is recent
File ./health/private/backups/20250618_120014-health-site_config_backup.json is recent
File ./health/private/backups/20250617_180013-health-database.sql.gz is recent
File ./health/private/backups/20250617_180013-health-site_config_backup.json is recent
Backup failed for Site site1. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['axessio', 'health', 'site1', 'viva'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7ea842bf3740>
      exit_code = 1
      rollback_callback = None
      site = 'site1'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7ea841a9e540>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ea84180aa70>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ea84180aa70>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7ea842d0efc0>
      db_name = '_5c0a108e8be5ba0f'
      user = '_5c0a108e8be5ba0f'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ea84180aa70>
      site = 'health'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7ea842d0efc0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b1c710>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7ea841833680>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_5c0a108e8be5ba0f'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7ea841ab85e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7ea841ab8680>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b1c710>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_5c0a108e8be5ba0f'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b1c710>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b1c710>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b1c710>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b1c710>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7ea841833890>
      user = '_5c0a108e8be5ba0f'
      password = ********
      host = '127.0.0.1'
      database = '_5c0a108e8be5ba0f'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7ea841bc4540>, <class 'int'>: <function escape_int at 0x7ea841bc45e0>, <class 'float'>: <function escape_float at 0x7ea841bc4680>, <class 'str'>: <function escape_str at 0x7ea841bc4900>, <class 'bytes'>: <function escape_bytes at 0x7ea841bc4860>, <class 'tuple'>: <function escape_sequence at 0x7ea841bc4400>, <class 'list'>: <function escape_sequence at 0x7ea841bc4400>, <class 'set'>: <function escape_sequence at 0x7ea841bc4400>, <class 'frozenset'>: <function escape_sequence at 0x7ea841bc4400>, <class 'dict'>: <function escape_dict at 0x7ea841bc4360>, <class 'NoneType'>: <function escape_None at 0x7ea841bc49a0>, <class 'datetime.date'>: <function escape_date at 0x7ea841bc4c20>, <class 'datetime.datetime'>: <function escape_datetime at 0x7ea841bc4b80>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7ea841bc4a40>, <class 'datetime.time'>: <function escape_time at 0x7ea841bc4ae0>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
      self = <pymysql.connections.Connection object at 0x7ea841833890>
      sock = None
      kwargs = {}
      exc = OperationalError(2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
File ./health/private/backups/20250618_120014-health-database.sql.gz is recent
File ./health/private/backups/20250618_120014-health-site_config_backup.json is recent
File ./health/private/backups/20250617_180013-health-database.sql.gz is recent
File ./health/private/backups/20250617_180013-health-site_config_backup.json is recent
Backup failed for Site viva. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['axessio', 'health', 'site1', 'viva'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7ea842bf3740>
      exit_code = 1
      rollback_callback = None
      site = 'viva'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7ea841a9e540>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ea841833730>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ea841833730>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7ea8419053d0>
      db_name = '_5c0a108e8be5ba0f'
      user = '_5c0a108e8be5ba0f'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ea841833730>
      site = 'health'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7ea8419053d0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b8aae0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7ea8418c5520>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_5c0a108e8be5ba0f'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7ea841ab85e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7ea841ab8680>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b8aae0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_5c0a108e8be5ba0f'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b8aae0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b8aae0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b8aae0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7ea841b8aae0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7ea8418c5730>
      user = '_5c0a108e8be5ba0f'
      password = ********
      host = '127.0.0.1'
      database = '_5c0a108e8be5ba0f'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7ea841bc4540>, <class 'int'>: <function escape_int at 0x7ea841bc45e0>, <class 'float'>: <function escape_float at 0x7ea841bc4680>, <class 'str'>: <function escape_str at 0x7ea841bc4900>, <class 'bytes'>: <function escape_bytes at 0x7ea841bc4860>, <class 'tuple'>: <function escape_sequence at 0x7ea841bc4400>, <class 'list'>: <function escape_sequence at 0x7ea841bc4400>, <class 'set'>: <function escape_sequence at 0x7ea841bc4400>, <class 'frozenset'>: <function escape_sequence at 0x7ea841bc4400>, <class 'dict'>: <function escape_dict at 0x7ea841bc4360>, <class 'NoneType'>: <function escape_None at 0x7ea841bc49a0>, <class 'datetime.date'>: <function escape_date at 0x7ea841bc4c20>, <class 'datetime.datetime'>: <function escape_datetime at 0x7ea841bc4b80>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7ea841bc4a40>, <class 'datetime.time'>: <function escape_time at 0x7ea841bc4ae0>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
      self = <pymysql.connections.Connection object at 0x7ea8418c5730>
      sock = None
      kwargs = {}
      exc = OperationalError(2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250619_110003-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-19 11:00:14.981454
Config  : ./axessio/private/backups/20250619_110003-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250619_110003-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
File ./health/private/backups/20250618_120014-health-database.sql.gz is recent
File ./health/private/backups/20250618_120014-health-site_config_backup.json is recent
File ./health/private/backups/20250617_180013-health-database.sql.gz is older than 24 hours
File ./health/private/backups/20250617_180013-health-site_config_backup.json is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_5c0a108e8be5ba0f --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _5c0a108e8be5ba0f | /usr/bin/gzip >> ./health/private/backups/20250619_120014-health-database.sql.gz

Backup Summary for health at 2025-06-19 12:25:26.985552
Config  : ./health/private/backups/20250619_120014-health-site_config_backup.json 225.0B
Database: ./health/private/backups/20250619_120014-health-database.sql.gz         6.3GiB
Backup for Site health has been successfully completed
File ./site1/private/backups/20250618_123034-site1-site_config_backup.json is recent
File ./site1/private/backups/20250618_123034-site1-database.sql.gz is recent
File ./site1/private/backups/20250617_182217-site1-site_config_backup.json is older than 24 hours
File ./site1/private/backups/20250617_182217-site1-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_0934226d26a34653 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0934226d26a34653 | /usr/bin/gzip >> ./site1/private/backups/20250619_122527-site1-database.sql.gz

Backup Summary for site1 at 2025-06-19 12:25:32.051409
Config  : ./site1/private/backups/20250619_122527-site1-site_config_backup.json 158.0B
Database: ./site1/private/backups/20250619_122527-site1-database.sql.gz         1.5MiB
Backup for Site site1 has been successfully completed
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/commands/__init__.py", line 29, in _func
    ret = f(frappe._dict(ctx.obj), *args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/backups.py", line 626, in new_backup
    odb.get_backup(older_than, ignore_files, force=force)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/backups.py", line 192, in get_backup
    self.set_backup_file_name()
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/backups.py", line 214, in set_backup_file_name
    enc = "-enc" if frappe.get_system_settings("encrypt_backup") else ""
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/__init__.py", line 2328, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/model/base_document.py", line 82, in import_controller
    doctype_info = frappe.db.get_value("DocType", doctype, ("module", "custom", "is_tree"), as_dict=True)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 619, in get_values
    out = self._get_values_from_table(
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 892, in _get_values_from_table
    return query.run(as_dict=as_dict, debug=debug, update=update, run=run, pluck=pluck)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 258, in sql
    traceback.print_stack()
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 114, in <module>
    main()
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/bench_helper.py", line 20, in main
    click.Group(commands=commands)(prog_name="bench")
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/env/lib/python3.12/site-packages/click/decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/commands/__init__.py", line 29, in _func
    ret = f(frappe._dict(ctx.obj), *args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/backups.py", line 626, in new_backup
    odb.get_backup(older_than, ignore_files, force=force)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/backups.py", line 192, in get_backup
    self.set_backup_file_name()
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/utils/backups.py", line 214, in set_backup_file_name
    enc = "-enc" if frappe.get_system_settings("encrypt_backup") else ""
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/__init__.py", line 2328, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/model/base_document.py", line 82, in import_controller
    doctype_info = frappe.db.get_value("DocType", doctype, ("module", "custom", "is_tree"), as_dict=True)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 644, in get_values
    out = self.get_values_from_single(
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 699, in get_values_from_single
    ).run(pluck=pluck, debug=debug, as_dict=False)
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/dev/frappe-bench/apps/frappe/frappe/database/database.py", line 258, in sql
    traceback.print_stack()
"Error in query:\n('SELECT `module`,`custom`,`is_tree` FROM `tabDocType` WHERE `name`=%(param1)s ORDER BY `modified` DESC LIMIT 1', {'param1': 'System Settings'})"
"Error in query:\n('SELECT `field`,`value` FROM `tabSingles` WHERE `field` IN (%(param1)s,%(param2)s,%(param3)s) AND `doctype`=%(param4)s', {'param1': 'module', 'param2': 'custom', 'param3': 'is_tree', 'param4': 'DocType'})"
Backup failed for Site viva. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['axessio', 'health', 'site1', 'viva'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x75ae9e3ef740>
      exit_code = 0
      rollback_callback = None
      site = 'viva'
      odb = <frappe.utils.backups.BackupGenerator object at 0x75ae9d2c2300>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75ae9d2f8b80>
  File "apps/frappe/frappe/utils/backups.py", line 626, in new_backup
    odb.get_backup(older_than, ignore_files, force=force)
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75ae9d2f8b80>
      odb = <frappe.utils.backups.BackupGenerator object at 0x75ae9d2f9340>
  File "apps/frappe/frappe/utils/backups.py", line 192, in get_backup
    self.set_backup_file_name()
      self = <frappe.utils.backups.BackupGenerator object at 0x75ae9d2f9340>
      older_than = 6
      ignore_files = True
      force = True
      last_db = False
      last_file = False
      last_private_file = False
      site_config_backup_path = False
  File "apps/frappe/frappe/utils/backups.py", line 214, in set_backup_file_name
    enc = "-enc" if frappe.get_system_settings("encrypt_backup") else ""
      self = <frappe.utils.backups.BackupGenerator object at 0x75ae9d2f9340>
      partial = ''
      ext = 'tar'
  File "apps/frappe/frappe/__init__.py", line 2328, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
      key = ********
      system_settings = None
  File "apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
      args = ('System Settings',)
      kwargs = {}
      key = ********
      doc = None
  File "apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
      args = ('System Settings',)
      kwargs = {}
      frappe = <module 'frappe' from 'apps/frappe/frappe/__init__.py'>
  File "apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
      args = ('System Settings',)
      kwargs = {}
      doctype = 'System Settings'
  File "apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
      doctype = 'System Settings'
      site_controllers = {}
  File "apps/frappe/frappe/model/base_document.py", line 82, in import_controller
    doctype_info = frappe.db.get_value("DocType", doctype, ("module", "custom", "is_tree"), as_dict=True)
      doctype = 'System Settings'
      Document = <class 'frappe.model.document.Document'>
      NestedSet = <class 'frappe.utils.nestedset.NestedSet'>
      module_name = 'Core'
  File "apps/frappe/frappe/database/database.py", line 515, in get_value
    result = self.get_values(
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x75ae9f775400>
      doctype = 'DocType'
      filters = 'System Settings'
      fieldname = ('module', 'custom', 'is_tree')
      ignore = None
      as_dict = True
      debug = False
      order_by = 'KEEP_DEFAULT_ORDERING'
      cache = False
      for_update = False
      run = True
      pluck = False
      distinct = False
      skip_locked = False
      wait = True
  File "apps/frappe/frappe/database/database.py", line 644, in get_values
    out = self.get_values_from_single(
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x75ae9f775400>
      doctype = 'DocType'
      filters = 'System Settings'
      fieldname = ('module', 'custom', 'is_tree')
      ignore = None
      as_dict = True
      debug = False
      order_by = 'modified'
      update = None
      cache = False
      for_update = False
      run = True
      pluck = False
      distinct = False
      limit = 1
      skip_locked = False
      wait = True
      out = None
      fields = ('module', 'custom', 'is_tree')
  File "apps/frappe/frappe/database/database.py", line 699, in get_values_from_single
    ).run(pluck=pluck, debug=debug, as_dict=False)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x75ae9f775400>
      fields = ('module', 'custom', 'is_tree')
      filters = 'System Settings'
      doctype = 'DocType'
      as_dict = True
      debug = False
      update = None
      run = True
      pluck = False
      distinct = False
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `field`,`value` FROM `tabSingles` WHERE `field` IN (%(param1)s,%(param2)s,%(param3)s) AND `doctype`=%(param4)s'
      args = ()
      kwargs = {'pluck': False, 'debug': False, 'as_dict': False}
      child_queries = []
      params = {'param1': 'module', 'param2': 'custom', 'param3': 'is_tree', 'param4': 'DocType'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x75ae9d2b45e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x75ae9d2b4680>
  File "apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x75ae9f775400>
      query = 'SELECT `field`,`value` FROM `tabSingles` WHERE `field` IN (%(param1)s,%(param2)s,%(param3)s) AND `doctype`=%(param4)s'
      values = {'param1': 'module', 'param2': 'custom', 'param3': 'is_tree', 'param4': 'DocType'}
      as_dict = False
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = False
      as_iterator = False
      trace_id = None
  File "env/lib/python3.12/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
      self = <pymysql.cursors.Cursor object at 0x75ae9d3bff50>
      query = "SELECT `field`,`value` FROM `tabSingles` WHERE `field` IN ('module','custom','is_tree') AND `doctype`='DocType'"
      args = {'param1': 'module', 'param2': 'custom', 'param3': 'is_tree', 'param4': 'DocType'}
  File "env/lib/python3.12/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
      self = <pymysql.cursors.Cursor object at 0x75ae9d3bff50>
      q = "SELECT `field`,`value` FROM `tabSingles` WHERE `field` IN ('module','custom','is_tree') AND `doctype`='DocType'"
      conn = <pymysql.connections.Connection object at 0x75ae9e37b4a0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
      self = <pymysql.connections.Connection object at 0x75ae9e37b4a0>
      sql = b"SELECT `field`,`value` FROM `tabSingles` WHERE `field` IN ('module','custom','is_tree') AND `doctype`='DocType'"
      unbuffered = False
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
      self = <pymysql.connections.Connection object at 0x75ae9e37b4a0>
      unbuffered = False
      result = <pymysql.connections.MySQLResult object at 0x75ae9d10a480>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
      self = <pymysql.connections.MySQLResult object at 0x75ae9d10a480>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x75ae9e37b4a0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xffz\x04#42S02Table \'_9c5dc86bed914016.tabSingles\' doesn\'t exist")
      packet_header = b';\x00\x00\x01'
      btrl = 59
      btrh = 0
      packet_number = 1
      bytes_to_read = 59
      recv_data = b"\xffz\x04#42S02Table '_9c5dc86bed914016.tabSingles' doesn't exist"
      packet = <pymysql.protocol.MysqlPacket object at 0x75ae9d10a3e0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x75ae9d10a3e0>
      errno = 1146
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xffz\x04#42S02Table '_9c5dc86bed914016.tabSingles' doesn't exist"
      errno = 1146
      errval = "Table '_9c5dc86bed914016.tabSingles' doesn't exist"
      errorclass = <class 'pymysql.err.ProgrammingError'>
pymysql.err.ProgrammingError: (1146, "Table '_9c5dc86bed914016.tabSingles' doesn't exist")
set -o pipefail; /usr/bin/mariadb-dump --user=_a70ef171fd422724 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a70ef171fd422724 | /usr/bin/gzip >> ./axessio/private/backups/20250619_170002-axessio-database.sql.gz

Backup Summary for axessio at 2025-06-19 17:00:13.472235
Config  : ./axessio/private/backups/20250619_170002-axessio-site_config_backup.json 158.0B
Database: ./axessio/private/backups/20250619_170002-axessio-database.sql.gz         20.6MiB
Backup for Site axessio has been successfully completed
