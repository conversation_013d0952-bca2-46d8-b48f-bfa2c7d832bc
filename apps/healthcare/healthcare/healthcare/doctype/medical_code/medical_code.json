{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "format:{medical_code_standard} {code}", "creation": "2025-06-15 21:18:16.155125", "doctype": "DocType", "engine": "InnoDB", "field_order": ["medical_code_standard", "code", "uri", "description"], "fields": [{"fieldname": "medical_code_standard", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Medical Code Standard", "options": "Medical Code Standard", "reqd": 1}, {"fieldname": "code", "fieldtype": "Data", "ignore_xss_filter": 1, "in_standard_filter": 1, "label": "Code", "reqd": 1, "unique": 1}, {"fieldname": "uri", "fieldtype": "Data", "label": "URI", "read_only": 1}, {"fieldname": "description", "fieldtype": "Small Text", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Description"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-16 11:23:29.118124", "modified_by": "Administrator", "module": "Healthcare", "name": "Medical Code", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}