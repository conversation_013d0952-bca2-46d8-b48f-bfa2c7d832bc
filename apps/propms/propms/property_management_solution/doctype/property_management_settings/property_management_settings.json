{"actions": [], "creation": "2019-01-10 09:46:13.704896", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["company", "section_break_2", "security_deposit_item", "column_break_5", "security_deposit_payment_type", "damage_charge_item", "section_break_9", "invoice_start_date", "section_break_12", "group_maintenance_job_items", "submit_maintenance_invoice", "make_single_invoice_on_lease", "make_invoice_schedule_up_to_tomorrow_only", "use_valid_from_date", "auto_submit_sales_invoice", "column_break_14", "maintenance_item_group", "section_break_17", "self_consumption_customer"], "fields": [{"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"fieldname": "security_deposit_item", "fieldtype": "Link", "label": "Security Deposit Item", "options": "<PERSON><PERSON>"}, {"fieldname": "damage_charge_item", "fieldtype": "Link", "label": "Damage Charge Item", "options": "<PERSON><PERSON>"}, {"fieldname": "security_deposit_payment_type", "fieldtype": "Link", "label": "Security Deposit Payment Type", "options": "Mode of Payment"}, {"default": "0", "fieldname": "group_maintenance_job_items", "fieldtype": "Check", "label": "Group Maintenance Job Items"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "section_break_12", "fieldtype": "Section Break"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "maintenance_item_group", "fieldtype": "Table MultiSelect", "label": "Maintenance Item Group", "options": "MultiSelect Item Group"}, {"default": "0", "fieldname": "submit_maintenance_invoice", "fieldtype": "Check", "label": "Submit Maintenance Invoice"}, {"fieldname": "section_break_17", "fieldtype": "Section Break"}, {"fieldname": "self_consumption_customer", "fieldtype": "Link", "label": "Self Consumption Customer", "options": "Customer"}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}, {"fieldname": "invoice_start_date", "fieldtype": "Date", "label": "Invoice Start Date"}, {"default": "0", "fieldname": "make_single_invoice_on_lease", "fieldtype": "Check", "label": "Make single invoice on lease"}, {"default": "0", "fieldname": "make_invoice_schedule_up_to_tomorrow_only", "fieldtype": "Check", "label": "Make invoice schedule up to tomorrow only"}, {"default": "0", "fieldname": "use_valid_from_date", "fieldtype": "Check", "label": "Use Valid From Date"}, {"default": "0", "fieldname": "auto_submit_sales_invoice", "fieldtype": "Check", "label": "Auto Submit Sales Invoice From Lease"}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-06-19 15:56:24.645492", "modified_by": "Administrator", "module": "Property Management Solution", "name": "Property Management Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Property Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}