{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:template", "beta": 1, "creation": "2017-10-05 14:59:55.438359", "description": "Procedure Template", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["disabled", "section_break_2", "template", "item", "item_code", "item_group", "healthcare_notes_template", "column_break_ulfw4", "points_of_care", "description", "column_break_5", "is_billable", "rate", "staff_role", "medical_department", "section_break_16", "company_options", "medical_coding_section", "medical_code_standard", "medical_code", "consumables", "consume_stock", "items", "sb_nursing_tasks", "clinical_procedure_check_list_template", "nursing_tasks", "sample_collection", "sample", "sample_uom", "sample_qty", "column_break_21", "sample_details", "change_in_item"], "fields": [{"fieldname": "template", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Template Name", "reqd": 1, "unique": 1}, {"fieldname": "item_code", "fieldtype": "Data", "label": "Item Code", "read_only_depends_on": "eval: !doc.__islocal ", "reqd": 1}, {"fieldname": "item_group", "fieldtype": "Link", "ignore_user_permissions": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Item Group", "options": "Item Group", "reqd": 1}, {"fieldname": "medical_department", "fieldtype": "Link", "label": "Medical Department", "options": "Medical Department"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_billable", "fieldtype": "Check", "label": "Is Billable"}, {"depends_on": "is_billable", "fieldname": "rate", "fieldtype": "Float", "in_list_view": 1, "in_standard_filter": 1, "label": "Rate", "mandatory_depends_on": "is_billable"}, {"fieldname": "description", "fieldtype": "Small Text", "ignore_xss_filter": 1, "label": "Description", "no_copy": 1, "reqd": 1}, {"default": "0", "fieldname": "consume_stock", "fieldtype": "Check", "label": "Allow Stock Consumption", "search_index": 1}, {"fieldname": "consumables", "fieldtype": "Section Break", "label": "Consumables"}, {"depends_on": "eval:doc.consume_stock == 1", "fieldname": "items", "fieldtype": "Table", "ignore_user_permissions": 1, "label": "Items", "options": "Clinical Procedure Item"}, {"collapsible": 1, "fieldname": "sample_collection", "fieldtype": "Section Break", "label": "Sample Collection"}, {"fieldname": "sample", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "<PERSON><PERSON>", "options": "Lab Test Sample"}, {"fetch_from": "sample.sample_uom", "fieldname": "sample_uom", "fieldtype": "Data", "label": "Sample UOM", "read_only": 1}, {"fieldname": "sample_qty", "fieldtype": "Float", "label": "Quantity"}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"fieldname": "sample_details", "fieldtype": "Small Text", "ignore_xss_filter": 1, "label": "Collection Details"}, {"default": "0", "fieldname": "change_in_item", "fieldtype": "Check", "hidden": 1, "label": "Change In Item", "no_copy": 1, "print_hide": 1, "report_hide": 1}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "item", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "<PERSON><PERSON>", "no_copy": 1, "options": "<PERSON><PERSON>", "read_only": 1}, {"collapsible": 1, "fieldname": "medical_coding_section", "fieldtype": "Section Break", "label": "Medical Coding"}, {"fieldname": "medical_code_standard", "fieldtype": "Link", "label": "Medical Code Standard", "options": "Code System"}, {"depends_on": "medical_code_standard", "fieldname": "medical_code", "fieldtype": "Link", "label": "Medical Code", "options": "Medical Code"}, {"fieldname": "staff_role", "fieldtype": "Link", "label": "Staff Role", "options": "Role"}, {"collapsible": 1, "fieldname": "sb_nursing_tasks", "fieldtype": "Section Break", "label": "Nursing Tasks"}, {"fieldname": "clinical_procedure_check_list_template", "fieldtype": "Link", "label": "Clinical Procedure Check List Template", "options": "Clinical Procedure Check List Template"}, {"fieldname": "nursing_tasks", "fieldtype": "Table", "label": "Nursing Tasks", "options": "Clinical Procedure Nursing Task"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "section_break_16", "fieldtype": "Section Break"}, {"fieldname": "company_options", "fieldtype": "Table", "label": "Companys Options", "options": "Healthcare Company Option", "reqd": 1}, {"fieldname": "column_break_ulfw4", "fieldtype": "Column Break"}, {"fieldname": "points_of_care", "fieldtype": "Link", "label": "Points of Care", "options": "Healthcare Points of Care", "reqd": 1}, {"fieldname": "healthcare_notes_template", "fieldtype": "Link", "label": "Healthcare Notes Template", "options": "Healthcare Notes Template"}], "links": [], "modified": "2025-06-06 18:50:59.060994", "modified_by": "Administrator", "module": "Hms Tz", "name": "Clinical Procedure Template", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Nursing User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "write": 1}], "restrict_to_domain": "Healthcare", "row_format": "Dynamic", "search_fields": "template", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "template", "track_changes": 1}