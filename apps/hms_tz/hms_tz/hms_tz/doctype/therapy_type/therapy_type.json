{"actions": [], "autoname": "field:therapy_type", "creation": "2020-03-29 20:48:31.715063", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["disabled", "section_break_2", "therapy_type", "item", "item_code", "item_name", "item_group", "column_break_12", "default_duration", "points_of_care", "description", "column_break_3", "is_billable", "rate", "medical_department", "healthcare_service_unit", "staff_role", "section_break_20", "company_options", "medical_coding_section", "medical_code_standard", "medical_code", "section_break_18", "therapy_for", "add_exercises", "section_break_6", "exercises", "change_in_item"], "fields": [{"fieldname": "therapy_type", "fieldtype": "Data", "in_list_view": 1, "label": "Therapy Type", "reqd": 1, "unique": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_billable", "fieldtype": "Check", "label": "Is Billable"}, {"depends_on": "eval:doc.is_billable;", "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate", "mandatory_depends_on": "eval:doc.is_billable;"}, {"fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Exercises"}, {"fieldname": "exercises", "fieldtype": "Table", "label": "Exercises", "options": "Exercise"}, {"fieldname": "default_duration", "fieldtype": "Int", "label": "De<PERSON>ult <PERSON> (In Minutes)"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "item", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "item_code", "fieldtype": "Data", "label": "Item Code", "reqd": 1, "set_only_once": 1}, {"fieldname": "item_group", "fieldtype": "Link", "label": "Item Group", "options": "Item Group", "reqd": 1}, {"fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "reqd": 1}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "medical_department", "fieldtype": "Link", "label": "Medical Department", "options": "Medical Department"}, {"default": "0", "fieldname": "change_in_item", "fieldtype": "Check", "hidden": 1, "label": "Change In Item", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "therapy_for", "fieldtype": "Table MultiSelect", "label": "Therapy For", "options": "Body Part Link"}, {"fieldname": "healthcare_service_unit", "fieldtype": "Link", "label": "Healthcare Service Unit", "options": "Healthcare Service Unit"}, {"depends_on": "eval: doc.therapy_for", "fieldname": "add_exercises", "fieldtype": "<PERSON><PERSON>", "label": "Add Exercises", "options": "add_exercises"}, {"fieldname": "section_break_18", "fieldtype": "Section Break"}, {"collapsible": 1, "fieldname": "medical_coding_section", "fieldtype": "Section Break", "label": "Medical Coding", "options": "Medical Coding"}, {"fieldname": "medical_code_standard", "fieldtype": "Link", "label": "Medical Code Standard", "options": "Code System"}, {"depends_on": "medical_code_standard", "fieldname": "medical_code", "fieldtype": "Link", "label": "Medical Code", "options": "Medical Code"}, {"fieldname": "staff_role", "fieldtype": "Link", "label": "Staff Role", "options": "Role"}, {"fieldname": "section_break_20", "fieldtype": "Section Break"}, {"fieldname": "company_options", "fieldtype": "Table", "label": "Companys Options", "options": "Healthcare Company Option", "reqd": 1}, {"default": "Phisiotherapy", "fieldname": "points_of_care", "fieldtype": "Link", "label": "Points of Care", "options": "Healthcare Points of Care"}], "links": [], "modified": "2025-06-06 19:11:23.492441", "modified_by": "Administrator", "module": "Hms Tz", "name": "Therapy Type", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "write": 1}], "quick_entry": 1, "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}