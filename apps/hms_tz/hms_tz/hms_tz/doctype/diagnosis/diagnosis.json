{"actions": [], "allow_copy": 1, "allow_import": 1, "allow_rename": 1, "autoname": "field:diagnosis", "beta": 1, "creation": "2017-02-15 12:23:59.341108", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["diagnosis", "estimated_duration", "medical_code_standard", "medical_code"], "fields": [{"fieldname": "diagnosis", "fieldtype": "Data", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Diagnosis", "reqd": 1, "unique": 1}, {"fieldname": "medical_code_standard", "fieldtype": "Link", "label": "Medical Code Standard", "options": "Code System", "search_index": 1}, {"fieldname": "medical_code", "fieldtype": "Link", "label": "Medical Code", "options": "Code Value"}, {"allow_in_quick_entry": 1, "fieldname": "estimated_duration", "fieldtype": "Duration", "hide_seconds": 1, "in_list_view": 1, "label": "Estimated Duration"}], "links": [], "modified": "2025-05-27 23:09:48.063153", "modified_by": "Administrator", "module": "Hms Tz", "name": "Diagnosis", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "write": 1}], "quick_entry": 1, "restrict_to_domain": "Healthcare", "row_format": "Dynamic", "search_fields": "diagnosis, medical_code", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}