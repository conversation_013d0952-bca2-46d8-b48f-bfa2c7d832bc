{"actions": [], "autoname": "naming_series:", "creation": "2018-07-11 17:48:51.404139", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["section_break_1", "naming_series", "patient", "patient_name", "gender", "blood_group", "dob", "mobile", "email", "phone", "column_break_8", "company", "status", "scheduled_date", "admitted_datetime", "expected_discharge", "sb_source", "source", "column_break_19", "referring_practitioner", "insurance_section", "insurance_subscription", "insurance_claim", "column_break_23", "insurance_company", "claim_status", "references", "admission_encounter", "admission_practitioner", "medical_department", "admission_ordered_for", "expected_length_of_stay", "admission_service_unit_type", "cb_admission", "primary_practitioner", "secondary_practitioner", "admission_instruction", "encounter_details_section", "chief_complaint", "column_break_29", "diagnosis", "investigations_section", "lab_test_prescription", "radiology_orders_section", "radiology_procedure_prescription", "procedures_section", "procedure_prescription", "medication_section", "drug_prescription", "rehabilitation_section", "therapy_plan", "therapies", "diet_recommendation_section", "diet_recommendation", "sb_inpatient_occupancy", "inpatient_occupancies", "btn_transfer", "sb_discharge_details", "discharge_ordered_date", "discharge_practitioner", "discharge_encounter", "discharge_date", "cb_discharge", "discharge_instructions", "followup_date", "sb_discharge_note", "discharge_note"], "fields": [{"fieldname": "section_break_1", "fieldtype": "Section Break"}, {"fieldname": "naming_series", "fieldtype": "Select", "hidden": 1, "label": "Series", "options": "HLC-INP-.YYYY.-"}, {"fieldname": "patient", "fieldtype": "Link", "in_list_view": 1, "label": "Patient", "options": "Patient", "reqd": 1, "set_only_once": 1}, {"fetch_from": "patient.patient_name", "fieldname": "patient_name", "fieldtype": "Data", "label": "Patient Name", "read_only": 1}, {"fetch_from": "patient.sex", "fieldname": "gender", "fieldtype": "Link", "label": "Gender", "options": "Gender", "read_only": 1}, {"fetch_from": "patient.blood_group", "fieldname": "blood_group", "fieldtype": "Select", "label": "Blood Group", "options": "\nA Positive\nA Negative\nAB Positive\nAB Negative\nB Positive\nB Negative\nO Positive\nO Negative", "read_only": 1}, {"fetch_from": "patient.dob", "fieldname": "dob", "fieldtype": "Date", "label": "Date of birth", "read_only": 1}, {"fetch_from": "patient.mobile", "fieldname": "mobile", "fieldtype": "Data", "label": "Mobile", "read_only": 1}, {"fetch_from": "patient.email", "fieldname": "email", "fieldtype": "Data", "label": "Email", "options": "Email", "read_only": 1}, {"fetch_from": "patient.phone", "fieldname": "phone", "fieldtype": "Data", "label": "Phone", "read_only": 1}, {"fieldname": "medical_department", "fieldtype": "Link", "label": "Medical Department", "options": "Medical Department", "set_only_once": 1}, {"fieldname": "primary_practitioner", "fieldtype": "Link", "label": "Healthcare Practitioner (Primary)", "options": "Healthcare Practitioner"}, {"fieldname": "secondary_practitioner", "fieldtype": "Link", "label": "Healthcare Practitioner (Secondary)", "options": "Healthcare Practitioner"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"default": "Admission Scheduled", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Admission Scheduled\nAdmitted\nDischarge Scheduled\nDischarged", "read_only": 1}, {"default": "Today", "fieldname": "scheduled_date", "fieldtype": "Date", "in_list_view": 1, "label": "Admission Schedule Date", "read_only": 1, "reqd": 1}, {"fieldname": "admission_ordered_for", "fieldtype": "Date", "label": "Admission Ordered For", "read_only": 1}, {"fieldname": "admitted_datetime", "fieldtype": "Datetime", "in_list_view": 1, "label": "Admitted Datetime", "read_only": 1}, {"depends_on": "eval:(doc.expected_length_of_stay > 0)", "fieldname": "expected_length_of_stay", "fieldtype": "Int", "label": "Expected Length of Stay", "set_only_once": 1}, {"fieldname": "expected_discharge", "fieldtype": "Date", "in_list_view": 1, "label": "Expected Discharge", "read_only": 1}, {"collapsible": 1, "fieldname": "references", "fieldtype": "Section Break", "label": "Admission Order Details"}, {"fieldname": "cb_admission", "fieldtype": "Column Break"}, {"fieldname": "admission_practitioner", "fieldtype": "Link", "label": "Healthcare Practitioner", "options": "Healthcare Practitioner", "read_only": 1}, {"fieldname": "admission_encounter", "fieldtype": "Link", "label": "Patient Encounter", "options": "Patient Encounter", "read_only": 1}, {"fieldname": "chief_complaint", "fieldtype": "Table MultiSelect", "label": "Chief <PERSON><PERSON><PERSON><PERSON>", "options": "Patient Encounter Symptom", "permlevel": 1}, {"fieldname": "admission_instruction", "fieldtype": "Small Text", "label": "Admission Instruction", "set_only_once": 1}, {"fieldname": "cb_discharge", "fieldtype": "Column Break"}, {"fieldname": "discharge_practitioner", "fieldtype": "Link", "label": "Healthcare Practitioner", "options": "Healthcare Practitioner", "read_only": 1}, {"fieldname": "discharge_encounter", "fieldtype": "Link", "label": "Patient Encounter", "options": "Patient Encounter", "read_only": 1}, {"collapsible": 1, "fieldname": "medication_section", "fieldtype": "Section Break", "label": "Medications", "permlevel": 1}, {"fieldname": "drug_prescription", "fieldtype": "Table", "options": "Drug Prescription", "permlevel": 1}, {"collapsible": 1, "fieldname": "investigations_section", "fieldtype": "Section Break", "label": "Investigations", "permlevel": 1}, {"fieldname": "lab_test_prescription", "fieldtype": "Table", "options": "Lab Prescription", "permlevel": 1}, {"collapsible": 1, "fieldname": "procedures_section", "fieldtype": "Section Break", "label": "Procedures", "permlevel": 1}, {"fieldname": "procedure_prescription", "fieldtype": "Table", "options": "Procedure Prescription", "permlevel": 1}, {"depends_on": "eval:(doc.status != \"Admission Scheduled\")", "fieldname": "sb_inpatient_occupancy", "fieldtype": "Section Break", "label": "Inpatient Occupancy"}, {"fieldname": "admission_service_unit_type", "fieldtype": "Link", "label": "Admission Service Unit Type", "options": "Healthcare Service Unit Type", "read_only": 1}, {"fieldname": "inpatient_occupancies", "fieldtype": "Table", "options": "Inpatient Occupancy", "read_only": 1}, {"fieldname": "btn_transfer", "fieldtype": "<PERSON><PERSON>", "label": "Transfer"}, {"depends_on": "eval:(doc.status == \"Discharge Scheduled\" || doc.status == \"Discharged\")", "fieldname": "sb_discharge_note", "fieldtype": "Section Break", "label": "Discharge Notes"}, {"fieldname": "discharge_note", "fieldtype": "Text Editor", "permlevel": 1}, {"fetch_from": "admission_encounter.company", "fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "options": "Company"}, {"collapsible": 1, "collapsible_depends_on": "eval:(doc.status == \"Admitted\")", "fieldname": "encounter_details_section", "fieldtype": "Section Break", "label": "Encounter Impression", "permlevel": 1}, {"fieldname": "column_break_29", "fieldtype": "Column Break"}, {"fieldname": "diagnosis", "fieldtype": "Table MultiSelect", "label": "Diagnosis", "options": "Patient Encounter Diagnosis", "permlevel": 1}, {"fieldname": "followup_date", "fieldtype": "Date", "label": "Follow Up Date"}, {"collapsible": 1, "depends_on": "eval:(doc.status == \"Discharge Scheduled\" || doc.status == \"Discharged\")", "fieldname": "sb_discharge_details", "fieldtype": "Section Break", "label": "Discharge <PERSON>s"}, {"fieldname": "discharge_instructions", "fieldtype": "Small Text", "label": "Discharge Instructions"}, {"fieldname": "discharge_ordered_date", "fieldtype": "Date", "in_list_view": 1, "label": "Discharge Ordered Date", "read_only": 1}, {"collapsible": 1, "fieldname": "rehabilitation_section", "fieldtype": "Section Break", "label": "Rehabilitation", "permlevel": 1}, {"fieldname": "therapy_plan", "fieldtype": "Link", "hidden": 1, "label": "Therapy Plan", "options": "Therapy Plan", "permlevel": 1, "read_only": 1}, {"fieldname": "therapies", "fieldtype": "Table", "label": "Therapies", "options": "Therapy Plan Detail", "permlevel": 1}, {"fieldname": "discharge_date", "fieldtype": "Date", "label": "Discharge Date", "read_only": 1}, {"fieldname": "sb_source", "fieldtype": "Section Break", "label": "Source"}, {"fieldname": "source", "fieldtype": "Select", "label": "Source", "options": "Direct\nReferral\nExternal Referral", "reqd": 1}, {"fieldname": "column_break_19", "fieldtype": "Column Break"}, {"fieldname": "referring_practitioner", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "Referring Practitioner", "options": "Healthcare Practitioner"}, {"collapsible": 1, "fieldname": "radiology_orders_section", "fieldtype": "Section Break", "label": "Radiology Orders"}, {"fieldname": "radiology_procedure_prescription", "fieldtype": "Table", "label": "Radiology Procedure Prescription", "options": "Radiology Procedure Prescription"}, {"collapsible": 1, "fieldname": "diet_recommendation_section", "fieldtype": "Section Break", "label": "Diet Recommendation"}, {"fieldname": "diet_recommendation", "fieldtype": "Table", "label": "Diet Recommendation", "options": "Diet Recommendation"}, {"fieldname": "insurance_section", "fieldtype": "Section Break", "label": "Insurance"}, {"fieldname": "column_break_23", "fieldtype": "Column Break"}, {"fetch_from": "insurance_subscription.insurance_company", "fieldname": "insurance_company", "fieldtype": "Link", "label": "Insurance Company", "options": "Healthcare Insurance Company", "read_only": 1}, {"fieldname": "insurance_subscription", "fieldtype": "Link", "label": "Insurance Subscription", "options": "Healthcare Insurance Subscription"}, {"fieldname": "insurance_claim", "fieldtype": "Link", "label": "Insurance Claim", "options": "Healthcare Insurance Claim", "read_only": 1}, {"fieldname": "claim_status", "fieldtype": "Select", "label": "Claim Status", "options": "\nPending\nApproved\nRejected", "read_only": 1}], "links": [], "modified": "2020-12-17 12:04:19.267977", "modified_by": "Administrator", "module": "Hms Tz", "name": "Inpatient Record", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Nursing User", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "role": "Physician", "write": 1}, {"permlevel": 1, "read": 1, "report": 1, "role": "Nursing User"}], "restrict_to_domain": "Healthcare", "search_fields": "patient", "sort_field": "modified", "sort_order": "DESC", "title_field": "patient", "track_changes": 1}