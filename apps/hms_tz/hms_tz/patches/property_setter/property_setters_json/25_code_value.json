[{"name": "Code Value-definition-label", "owner": "Administrator", "creation": "2025-05-26 14:40:24.704505", "modified": "2025-05-26 14:40:24.704505", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "doc_type": "Code Value", "field_name": "definition", "property": "label", "property_type": "Data", "value": "Description", "doctype": "Property Setter"}, {"name": "Code Value-code_value-label", "owner": "Administrator", "creation": "2025-05-26 14:40:24.579246", "modified": "2025-05-26 14:40:24.579246", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "doc_type": "Code Value", "field_name": "code_value", "property": "label", "property_type": "Data", "value": "Medical Code", "doctype": "Property Setter"}, {"name": "Code Value-code_system-label", "owner": "Administrator", "creation": "2025-05-26 14:40:24.336914", "modified": "2025-05-26 14:40:24.336914", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "doc_type": "Code Value", "field_name": "code_system", "property": "label", "property_type": "Data", "value": "Medical Code Standard", "doctype": "Property Setter"}, {"name": "Code Value-main-autoname", "owner": "Administrator", "creation": "2025-05-26 14:35:49.854122", "modified": "2025-05-26 14:35:49.854122", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "DocType", "doc_type": "Code Value", "property": "autoname", "property_type": "Data", "value": "format: {code_system} {code_value}", "doctype": "Property Setter"}, {"name": "Code Value-main-naming_rule", "owner": "Administrator", "creation": "2025-05-26 14:35:49.728371", "modified": "2025-05-26 14:35:49.728371", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "DocType", "doc_type": "Code Value", "property": "naming_rule", "property_type": "Data", "value": "Expression", "doctype": "Property Setter"}, {"name": "Code Value-main-search_fields", "owner": "Administrator", "creation": "2025-05-26 14:35:49.615380", "modified": "2025-05-26 14:35:49.615380", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "DocType", "doc_type": "Code Value", "property": "search_fields", "property_type": "Data", "value": "code_value, definition, level", "doctype": "Property Setter"}, {"name": "Code Value-main-field_order", "owner": "Administrator", "creation": "2025-05-26 14:33:07.889302", "modified": "2025-05-26 14:33:07.889302", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 0, "doctype_or_field": "DocType", "doc_type": "Code Value", "property": "field_order", "property_type": "Data", "value": "[\"section_break_mygg\", \"code_system\", \"code_value\", \"value_set\", \"definition\", \"column_break_cmkw\", \"status\", \"version\", \"level\", \"official_url\", \"system_uri\", \"canonical_mapping\", \"column_break_yb7td\", \"custom\", \"complete\", \"immutable\", \"experimental\", \"is_non_specific\", \"display\"]", "doctype": "Property Setter"}]