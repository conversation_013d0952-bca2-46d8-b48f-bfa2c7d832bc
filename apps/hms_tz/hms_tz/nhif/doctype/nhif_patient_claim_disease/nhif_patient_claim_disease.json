{"actions": [], "creation": "2020-12-17 03:35:48.012453", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["diagnosis_type", "status", "medical_code", "disease_code", "description", "column_break_7", "patient_encounter", "codification_table", "item_crt_by", "date_created"], "fields": [{"fetch_from": "medical_code.code", "fetch_if_empty": 1, "fieldname": "disease_code", "fieldtype": "Data", "in_list_view": 1, "label": "Disease Code", "permlevel": 1}, {"fieldname": "date_created", "fieldtype": "Datetime", "in_list_view": 1, "label": "Date Created", "permlevel": 1}, {"fieldname": "diagnosis_type", "fieldtype": "Data", "label": "Diagnosis Type", "permlevel": 1}, {"fieldname": "medical_code", "fieldtype": "Link", "in_list_view": 1, "label": "Medical Code", "options": "Code Value", "permlevel": 1}, {"fetch_from": "medical_code.description", "fieldname": "description", "fieldtype": "Data", "in_list_view": 1, "label": "Description", "read_only": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "codification_table", "fieldtype": "Link", "label": "Codification Table", "options": "Codification Table", "read_only": 1}, {"fieldname": "patient_encounter", "fieldtype": "Link", "label": "Patient Encounter", "options": "Patient Encounter", "read_only": 1}, {"fieldname": "item_crt_by", "fieldtype": "Data", "in_list_view": 1, "label": "Created By", "read_only": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Provisional\nFinal"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-05-27 23:14:15.953317", "modified_by": "Administrator", "module": "NHIF", "name": "NHIF Patient Claim Disease", "owner": "Administrator", "permissions": [], "quick_entry": 1, "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}