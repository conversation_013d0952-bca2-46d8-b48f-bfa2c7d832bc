# -*- coding: utf-8 -*-
# Copyright (c) 2021, Aakvatech Ltd. and contributors
# For license information, please see license.txt

"""
Education Domain Functions
Contains all education-related hook functions including program enrollment, student applicant, etc.
"""

from __future__ import unicode_literals 
import frappe
from frappe.model.document import Document
from frappe.utils import today
from frappe import _
from education.education.doctype.program_enrollment.program_enrollment import ProgramEnrollment
from csf_tz.custom_api import print_out


# =============================================================================
# PROGRAM ENROLLMENT FUNCTIONS
# =============================================================================

def create_course_enrollments(self):
    student = frappe.get_doc("Student", self.student)
    program = frappe.get_doc("Program", self.program)
    course_list = [course.course for course in program.courses]
    for course_name in course_list:
        student.enroll_in_course(
            course_name=course_name, program_enrollment=self.name)


def create_course_enrollments_override(doc, method):
    ProgramEnrollment.create_course_enrollments = create_course_enrollments


@frappe.whitelist()
def get_fee_schedule(program, academic_year, academic_term=None, student_category=None):
    """Returns Fee Schedule.

    :param program: Program.
    :param student_category: Student Category   
    :param academic_year
    :param academic_term
    """
    fs = frappe.get_list("Program Fee", fields=["academic_term", "fee_structure", "due_date", "amount"],
                         filters={"parent": program, "student_category": student_category}, parent_doctype="Program Enrollment", order_by="idx")

    fees_list = []
    for i in fs:
        fs_academic_year = frappe.get_value(
            "Fee Structure", i["fee_structure"], "academic_year") or ""
        fs_academic_term = "False"
        if academic_term:
            fs_academic_term = frappe.get_value(
                "Fee Structure", i["fee_structure"], "academic_term") or ""
        if fs_academic_term != "False":
            if fs_academic_term == academic_term and fs_academic_year == academic_year:
                fees_list.append(i)
        else:
            if fs_academic_year == academic_year:
                fees_list.append(i)

    return fees_list


def validate_submit_program_enrollment(doc, method):
    if not doc.student_category:
        frappe.throw(_("Please set Student Category"))


# =============================================================================
# STUDENT APPLICANT FUNCTIONS
# =============================================================================

def make_student_applicant_fees(doc, method):
    if doc.docstatus != 1:
        return
    if doc.application_status != "Awaiting Registration Fees" or doc.student_applicant_fee:
        return
    fee_structure = frappe.get_doc("Fee Structure", doc.fee_structure)
    student_name = doc.first_name
    if doc.middle_name:
        student_name += " " + doc.middle_name
    if doc.last_name:
        student_name += " " + doc.last_name
    
    fee_doc =frappe.get_doc ({
        'doctype': 'Student Applicant Fees',
        'student': doc.name,
        'student_name': student_name,
        'fee_schedule': None,
        'company': fee_structure.company,
        'posting_date': today(),
        'due_date': today(),
        'program_enrollment': doc.program_enrollment,
        'program': fee_structure.program, 
        'student_batch': None, 
        'student_email': doc.student_email_id, 
        'student_category': fee_structure.student_category,
        'academic_term': fee_structure.academic_term,
        'academic_year': fee_structure.academic_year, 
        'currency': frappe.get_value("Company", fee_structure.company, "default_currency"), 
        'fee_structure': doc.fee_structure, 
        'grand_total': fee_structure.total_amount, 
        'receivable_account': fee_structure.receivable_account, 
        'income_account': fee_structure.income_account, 
        'cost_center': fee_structure.cost_center, 
    })

    fee_doc.flags.ignore_permissions = True
    frappe.flags.ignore_account_permission = True
    fee_doc.save()
    callback_token = fee_doc.callback_token
    doc.bank_reference = fee_doc.bank_reference or "None"
    doc.student_applicant_fee = fee_doc.name or "None"
    doc.db_update()
    fee_doc.reload()
    fee_doc.callback_token = callback_token
    fee_doc.bank_reference = doc.bank_reference
    fee_doc.db_update()
    fee_doc.submit()
