# -*- coding: utf-8 -*-
# Copyright (c) 2021, Aakvatech Ltd. and contributors
# For license information, please see license.txt

"""
Purchasing Domain Functions
Contains all purchasing-related hook functions including purchase orders, etc.
"""

import frappe
from frappe import _


# =============================================================================
# PURCHASE ORDER FUNCTIONS
# =============================================================================

@frappe.whitelist()
def update_po_status(status, name):
    from erpnext.buying.doctype.purchase_order.purchase_order import update_status

    if status != "Submitted":
        update_status(status, name)
        return

    csf_tz_settings = frappe.get_doc("CSF TZ Settings")
    if csf_tz_settings.allow_reopen_of_po_based_on_role == 1:
        roles = frappe.get_roles()
        if csf_tz_settings.role_to_reopen_po and csf_tz_settings.role_to_reopen_po in roles:
            update_status(status, name)
        else:
            frappe.throw(_(f"You are not allowed to reopen this Purchase Order: <b>{name}</b>"))


@frappe.whitelist()
def close_or_unclose_purchase_orders(names, status):
    from erpnext.buying.doctype.purchase_order.purchase_order import close_or_unclose_purchase_orders as original_close_or_unclose
    
    # Call the original function
    return original_close_or_unclose(names, status)
