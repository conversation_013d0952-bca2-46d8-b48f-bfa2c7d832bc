# -*- coding: utf-8 -*-
# Copyright (c) 2021, Aakvatech Ltd. and contributors
# For license information, please see license.txt

"""
Stock Domain Functions
Contains all stock and inventory-related hook functions including stock entries, material requests, item revaluation, etc.
"""

from __future__ import unicode_literals
import frappe
from frappe import _
from frappe.utils import flt, add_days, nowdate, create_batch
from frappe.query_builder import DocType
from frappe.utils.background_jobs import enqueue
from erpnext.stock.doctype.stock_entry.stock_entry import StockEntry
from erpnext.stock.doctype.material_request.material_request import update_status
import os
from PyPDF2 import PdfFileWriter
from csf_tz import console


# =============================================================================
# STOCK ENTRY FUNCTIONS
# =============================================================================

def validate_with_material_request(self):
    bypass_material_request_validation = frappe.get_value("Company", self.company,"bypass_material_request_validation") or 0
    if bypass_material_request_validation:
        return
    for item in self.get("items"):
        if item.material_request:
            mreq_item = frappe.db.get_value("Material Request Item",
                {"name": item.material_request_item, "parent": item.material_request},
                ["item_code", "warehouse", "idx"], as_dict=True)
            if mreq_item.item_code != item.item_code or \
            mreq_item.warehouse != (item.s_warehouse if self.purpose== "Material Issue" else item.t_warehouse):
                frappe.throw(_("Item or Warehouse for row {0} does not match Material Request").format(item.idx),
                    frappe.MappingMismatchError)


def validate_with_material_request_override(doc, method):
    StockEntry.validate_with_material_request = validate_with_material_request


def import_from_bom(self, method):
    if self.stock_entry_type == "Manufacture" and self.bom_no:
        bom = frappe.get_doc("BOM", self.bom_no)
        for d in bom.additional_costs:
            self.append("additional_costs", {
                "expense_account": d.expense_account,
                "amount": d.cost_per_unit,
                "base_amount": d.cost_per_unit,
                "description": d.cost_type
            })


# =============================================================================
# MATERIAL REQUEST FUNCTIONS
# =============================================================================

cp = DocType("Company")
mr = DocType("Material Request")

@frappe.whitelist()
def update_mr_status(name, status):
    if status != "Submitted":
        update_status(name, status)
        return

    csf_tz_settings = frappe.get_doc("CSF TZ Settings")
    if csf_tz_settings.allow_reopen_of_material_request_based_on_role == 1:
        roles = frappe.get_roles()
        if csf_tz_settings.role_to_reopen_material_request and csf_tz_settings.role_to_reopen_material_request in roles:
            update_status(name, status)
        else:
            frappe.throw(_(f"You are not allowed to reopen this Material Request: <b>{name}</b>"))


def auto_close_material_request():
    """
    Auto close Material Request based on settings specified on Company under section of stock settings
    """

    def close_request_docs(date_before, row):
        material_requests = (
            frappe.qb.from_(mr)
            .select(
                mr.name
            )
            .where(
                (mr.docstatus == 1)
                & (mr.company == row.name)
                & (mr.status != "Stopped")
                & (mr.transaction_date <= date_before)
            )
        ).run(as_dict=True)

        if len(material_requests) == 0:
            return

        for records in create_batch(material_requests, 100):
            for record in records:
                try:
                    material_request_doc = frappe.get_doc("Material Request", record.name)
                    material_request_doc.update_status("Stopped")
                
                except Exception as e:
                    frappe.log_error(frappe.get_traceback(), f"Auto Close Material Request Error: {record.name}")

    company_details = (
        frappe.qb.from_(cp)
        .select(
            cp.name,
            cp.close_material_request_after
        )
        .where(
            cp.enable_auto_close_material_request == 1
        )
    ).run(as_dict=True)
    
    if len(company_details) == 0:
        return
    
    for row in company_details:
        before_days = add_days(nowdate(), -row.close_material_request_after)
        close_request_docs(before_days, row)


# =============================================================================
# ITEM REVALUATION FUNCTIONS
# =============================================================================

@frappe.whitelist()
def get_data(filters):
    filters = frappe._dict(filters)
    data = get_stock_ledger_entries(filters)
    itewise_balance_qty = {}

    for row in data:
        key = (row.item_code, row.warehouse)
        itewise_balance_qty.setdefault(key, []).append(row)

    res = validate_data(itewise_balance_qty)
    return res


def validate_data(itewise_balance_qty):
    res = []
    for key, data in itewise_balance_qty.items():
        row = get_incorrect_data(data)
        if row:
            res.append(row)

    return res


def get_incorrect_data(data):
    balance_qty = 0.0
    for row in data:
        balance_qty += row.actual_qty
        if row.voucher_type == "Stock Reconciliation" and not row.batch_no:
            balance_qty = flt(row.qty_after_transaction)

        row.expected_balance_qty = balance_qty
        if abs(flt(row.expected_balance_qty) - flt(row.qty_after_transaction)) > 0.5:
            row.differnce = abs(
                flt(row.expected_balance_qty) - flt(row.qty_after_transaction)
            )
            return row


def get_stock_ledger_entries(report_filters):
    filters = {"is_cancelled": 0}
    fields = [
        "name",
        "voucher_type",
        "voucher_no",
        "item_code",
        "actual_qty",
        "posting_date",
        "posting_time",
        "company",
        "warehouse",
        "qty_after_transaction",
        "batch_no",
    ]

    for field in ["warehouse", "item_code", "company"]:
        if report_filters.get(field):
            filters[field] = report_filters.get(field)

    return frappe.get_all(
        "Stock Ledger Entry",
        fields=fields,
        filters=filters,
        order_by="timestamp(posting_date, posting_time) asc, creation asc",
    )


def process_incorrect_balance_qty():
    data = get_data({})
    if len(data) > 0:
        rec = frappe._dict(data[0])
        doc = frappe.new_doc("Repost Item Valuation")
        doc.based_on = "Transaction"
        doc.voucher_type = rec.voucher_type
        doc.voucher_no = rec.voucher_no
        doc.posting_date = rec.posting_date
        doc.posting_time = rec.posting_time
        doc.company = rec.company
        doc.warehouse = rec.warehouse
        doc.allow_negative_stock = 1
        doc.docstatus = 1
        doc.insert(ignore_permissions=True)
        frappe.db.commit()


# =============================================================================
# ITEM REPOSTING FUNCTIONS
# =============================================================================

def execute_item_reposting():
    for doctype in ('repost_item_valuation', 'stock_entry_detail', 'purchase_receipt_item',
            'purchase_invoice_item', 'delivery_note_item', 'sales_invoice_item', 'packed_item'):
        frappe.reload_doc('stock', 'doctype', doctype)
    frappe.reload_doc('buying', 'doctype', 'purchase_receipt_item_supplied')


# =============================================================================
# LANDED COST VOUCHER FUNCTIONS
# =============================================================================

@frappe.whitelist()
def get_landed_cost_expenses(import_file=None):
    if not import_file:
        return

    je_landed_cost = frappe.db.sql("""SELECT jea.account as 'expense_account', je.title as 'description', jea.debit as 'amount'
                     FROM `tabJournal Entry` je
                     INNER JOIN `tabJournal Entry Account` jea
                         ON jea.parent = je.name
                     WHERE je.import_file = %s
                       AND je.docstatus = 1
                       AND jea.debit > 0""", import_file, as_dict=1)
    # frappe.db.sql("""update `tabSales Order Item` set delivered_qty = 0
    # 			where parent = %s""", so.name)
    pinv_landed_cost = frappe.db.sql("""SELECT pii.expense_account as 'expense_account', pi.title as 'description', pii.base_net_amount as 'amount'
                     FROM `tabPurchase Invoice` pi
                     INNER JOIN `tabPurchase Invoice Item` pii
                         ON pii.parent = pi.name
                     WHERE pi.import_file = %s
                       AND pi.docstatus = 1;""", import_file, as_dict=1)
    return je_landed_cost + pinv_landed_cost


def total_amount(doc, method):
    for item in doc.items:  
        if item.amount and item.applicable_charges:
            item.custom_total_amount = item.amount + item.applicable_charges
        else:
            item.custom_total_amount = 0
            
    if doc.items:
        grand_total = 0
        for item in doc.items:
            grand_total += item.custom_total_amount or 0
        doc.custom_grand_total = grand_total
    else:
        doc.custom_grand_total = 0


# =============================================================================
# CUSTOM ITEM DETAILS FUNCTIONS
# =============================================================================

@frappe.whitelist()
def custom_get_item_details(args, doc=None, for_validate=False, overwrite_warehouse=True):
    from erpnext.stock.get_item_details import get_item_details
    
    # Call the original get_item_details method
    response = get_item_details(args, doc, for_validate, overwrite_warehouse)

    # Check CSF TZ Settings
    override_sales_invoice_qty = frappe.db.get_single_value("CSF TZ Settings", "override_sales_invoice_qty")

    # If the setting is enabled, remove the qty field from the response
    if override_sales_invoice_qty:
        response.pop("qty", None)  

    return response
