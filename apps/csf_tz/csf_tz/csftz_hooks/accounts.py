# -*- coding: utf-8 -*-
# Copyright (c) 2021, Aakvatech Ltd. and contributors
# For license information, please see license.txt

"""
Accounts Domain Functions
Contains all accounting and finance-related hook functions including payment entries, bank charges, customer functions, etc.
"""

from __future__ import unicode_literals
import frappe
import json
from frappe import _
from frappe.utils import nowdate, getdate
from erpnext.accounts.utils import get_outstanding_invoices, get_account_currency
from erpnext.setup.utils import get_exchange_rate
from erpnext.controllers.accounts_controller import get_supplier_block_status
from erpnext.accounts.doctype.payment_entry.payment_entry import (
    get_orders_to_be_billed,
    get_negative_outstanding_invoices,
)
from frappe import ValidationError, _, qb, scrub, throw


# =============================================================================
# PAYMENT ENTRY FUNCTIONS
# =============================================================================

@frappe.whitelist()
def get_outstanding_reference_documents(args):
    # Check if the feature is disabled in CSF TZ Settings
    if frappe.db.get_single_value("CSF TZ Settings", "disable_get_outstanding_functionality"):
        return []
    
    if isinstance(args, str):
        args = json.loads(args)

    if args.get("party_type") == "Member":
        return

    ple = qb.DocType("Payment Ledger Entry")
    common_filter = []
    accounting_dimensions_filter = []
    posting_and_due_date = []

    # confirm that Supplier is not blocked
    if args.get("party_type") == "Supplier":
        supplier_status = get_supplier_block_status(args["party"])
        if supplier_status["on_hold"]:
            if supplier_status["hold_type"] == "All":
                return []
            elif supplier_status["hold_type"] == "Payments":
                if (
                    not supplier_status["release_date"]
                    or getdate(nowdate()) <= supplier_status["release_date"]
                ):
                    return []

    party_account_currency = get_account_currency(args.get("party_account"))
    company_currency = frappe.get_cached_value(
        "Company", args.get("company"), "default_currency"
    )

    # Get positive outstanding sales /purchase invoices
    condition = ""
    if args.get("voucher_type") and args.get("voucher_no"):
        condition = " and voucher_type={0} and voucher_no={1}".format(
            frappe.db.escape(args["voucher_type"]), frappe.db.escape(args["voucher_no"])
        )
        common_filter.append(ple.voucher_type == args["voucher_type"])
        common_filter.append(ple.voucher_no == args["voucher_no"])

    # Add cost center condition
    # if args.get("cost_center"):
    # 	condition += " and cost_center='%s'" % args.get("cost_center")
    # 	accounting_dimensions_filter.append(ple.cost_center == args.get("cost_center"))

    date_fields_dict = {
        "posting_date": ["from_posting_date", "to_posting_date"],
        "due_date": ["from_due_date", "to_due_date"],
    }

    for fieldname, date_fields in date_fields_dict.items():
        if args.get(date_fields[0]) and args.get(date_fields[1]):
            condition += " and {0} between '{1}' and '{2}'".format(
                fieldname, args.get(date_fields[0]), args.get(date_fields[1])
            )
            posting_and_due_date.append(
                ple[fieldname][args.get(date_fields[0]) : args.get(date_fields[1])]
            )

    if args.get("company"):
        condition += " and company = {0}".format(frappe.db.escape(args.get("company")))
        common_filter.append(ple.company == args.get("company"))

    party_account = [args.get("party_account")]
    outstanding_invoices = get_outstanding_invoices(
        args.get("party_type"),
        args.get("party"),
        party_account,
        common_filter=common_filter,
        posting_date=posting_and_due_date,
        min_outstanding=args.get("outstanding_amt_greater_than"),
        max_outstanding=args.get("outstanding_amt_less_than"),
        accounting_dimensions=accounting_dimensions_filter,
    )
    from erpnext.accounts.doctype.payment_entry.payment_entry import (
        split_invoices_based_on_payment_terms,
    )

    outstanding_invoices = split_invoices_based_on_payment_terms(
        outstanding_invoices, args.get("company")
    )

    for d in outstanding_invoices:
        d["exchange_rate"] = 1
        d["posting_date"] = frappe.db.get_value(
            d.voucher_type, d.voucher_no, "posting_date"
        )
        if party_account_currency != company_currency:
            if d.voucher_type in frappe.get_hooks("invoice_doctypes"):
                d["exchange_rate"] = frappe.db.get_value(
                    d.voucher_type, d.voucher_no, "conversion_rate"
                )
            elif d.voucher_type == "Journal Entry":
                d["exchange_rate"] = get_exchange_rate(
                    party_account_currency, company_currency, d.posting_date
                )
        if d.voucher_type in ("Purchase Invoice"):
            d["bill_no"] = frappe.db.get_value(d.voucher_type, d.voucher_no, "bill_no")

    # Get all SO / PO which are not fully billed or against which full advance not paid
    orders_to_be_billed = []
    # orders_to_be_billed = get_orders_to_be_billed(
    #     args.get("posting_date"),
    #     args.get("party_type"),
    #     args.get("party"),
    #     args.get("company"),
    #     party_account_currency,
    #     company_currency,
    #     filters=args,
    # )

    # Get negative outstanding sales /purchase invoices
    negative_outstanding_invoices = []
    if args.get("party_type") != "Employee" and not args.get("voucher_no"):
        negative_outstanding_invoices = get_negative_outstanding_invoices(
            args.get("party_type"),
            args.get("party"),
            args.get("party_account"),
            party_account_currency,
            company_currency,
            condition=condition,
        )

    data = negative_outstanding_invoices + outstanding_invoices + orders_to_be_billed

    if not data:
        frappe.msgprint(
            _(
                "No outstanding invoices found for the {0} {1} which qualify the filters you have specified."
            ).format(_(args.get("party_type")).lower(), frappe.bold(args.get("party")))
        )

    return data


@frappe.whitelist()
def get_outstanding_sales_orders(args):
    if isinstance(args, str):
        args = json.loads(args)

    if args.get("party_type") == "Member":
        return

    if args.get("party_type") != "Customer":
        frappe.throw(_("Sales Orders can only be fetched for Customer"))

    # confirm that Supplier is not blocked (not needed for Customer, but keeping for consistency)
    if args.get("party_type") == "Supplier":
        supplier_status = get_supplier_block_status(args["party"])
        if supplier_status["on_hold"]:
            if supplier_status["hold_type"] == "All":
                return []
            elif supplier_status["hold_type"] == "Payments":
                if (
                    not supplier_status["release_date"]
                    or getdate(nowdate()) <= supplier_status["release_date"]
                ):
                    return []

    party_account_currency = get_account_currency(args.get("party_account"))
    company_currency = frappe.get_cached_value(
        "Company", args.get("company"), "default_currency"
    )

    # Get all SO which are not fully billed or against which full advance not paid
    orders_to_be_billed = get_orders_to_be_billed(
        args.get("posting_date"),
        args.get("party_type"),
        args.get("party"),
        args.get("company"),
        party_account_currency,
        company_currency,
        filters=args,
    )

    for d in orders_to_be_billed:
        d["exchange_rate"] = 1
        if party_account_currency != company_currency:
            if d.voucher_type in frappe.get_hooks("invoice_doctypes"):
                d["exchange_rate"] = frappe.db.get_value(
                    d.voucher_type, d.voucher_no, "conversion_rate"
                )
            else:
                d["exchange_rate"] = get_exchange_rate(
                    party_account_currency, company_currency, d.posting_date
                )

    # Add posting date
    for d in orders_to_be_billed:
        d["posting_date"] = frappe.db.get_value(
            d.voucher_type, d.voucher_no, "transaction_date" if d.voucher_type == "Sales Order" else "posting_date"
        )
        
        # Add due date (if available)
        if d.voucher_type == "Sales Order":
            d["due_date"] = frappe.db.get_value(d.voucher_type, d.voucher_no, "delivery_date") or ""

    if not orders_to_be_billed:
        frappe.msgprint(
            _(
                "No outstanding Sales Orders found for the {0} {1} which qualify the filters you have specified."
            ).format(_(args.get("party_type")).lower(), frappe.bold(args.get("party")))
        )

    return orders_to_be_billed


def validate_payment_entry(self, method):
    company = frappe.get_cached_doc("Company", self.company)

    if company.restrict_unallocated_amount_for_supplier and self.restrict_unallocated_amount_for_supplier:
        if self.unallocated_amount > 0: 
            frappe.throw(_("Cannot submit Payment Entry <b>{0}</b> with unallocated amount.").format(self.name))


# =============================================================================
# BANK CHARGES FUNCTIONS
# =============================================================================

def validate_bank_charges_account(payment_entry, method):
    """Ensure the Default Bank Charges Account is set before submitting Payment Entry"""

    if payment_entry.bank_charges and payment_entry.bank_charges > 0:

        company = payment_entry.company
        bank_charges_account = frappe.db.get_value("Company", company, "default_bank_charges_account")

        if not bank_charges_account:
            frappe.throw("Default Bank Charges Account is not set in Company settings. Please set it before submitting.")


def create_bank_charges_journal(payment_entry, method):
    """Creates a journal entry if bank charges are greater than 0"""

    if payment_entry.bank_charges and payment_entry.bank_charges > 0:

        company = payment_entry.company
        bank_account = payment_entry.paid_from
        bank_charges_account = frappe.db.get_value("Company", company, "default_bank_charges_account")

        if not bank_charges_account:
            frappe.throw("Default Bank Charges Account is not set in Company settings. Please set it before submitting.")

        # Create Journal Entry
        journal_entry = frappe.get_doc({
            "doctype": "Journal Entry",
            "voucher_type": "Bank Entry",
            "company": company,
            "posting_date": payment_entry.posting_date,
            "accounts": [
                {
                    "account": bank_charges_account,
                    "debit_in_account_currency": payment_entry.bank_charges,
                    "credit_in_account_currency": 0
                },
                {
                    "account": bank_account,
                    "debit_in_account_currency": 0,
                    "credit_in_account_currency": payment_entry.bank_charges
                }
            ],
            "user_remark": f"Bank charges for Payment Entry {payment_entry.name}",
            "reference_doctype": "Payment Entry",
            "reference_name": payment_entry.name,
            "cheque_no": payment_entry.name,  
            "cheque_date": nowdate()  
        })
        journal_entry.insert()
        journal_entry.submit()

        payment_entry.db_set("bank_charges_journal_entry", journal_entry.name)


# =============================================================================
# CUSTOMER FUNCTIONS
# =============================================================================

@frappe.whitelist()
def get_customer_total_unpaid_amount(customer, company=None):
    if not customer:
        return 0
    company_condition = ""
    if company:
        company_condition = " and company = '{0}'".format(company)
    company_wise_total_unpaid = frappe._dict(
        frappe.db.sql(
            """
        select company, sum(debit_in_account_currency) - sum(credit_in_account_currency)
        from `tabGL Entry`
        where party_type = %s and party=%s
        and is_cancelled = 0 {0}
        group by company""".format(company_condition),
            ("Customer", customer),
        )
    )
    total_unpaid = 0
    if company:
        total_unpaid = company_wise_total_unpaid.get(company, 0)
    else:
        total_unpaid = sum(company_wise_total_unpaid.values())

    total_unpaid = frappe.format_value(total_unpaid, "Float")

    frappe.msgprint(_("Total Unpaid Amount is {0}").format(total_unpaid))
    return total_unpaid
