# -*- coding: utf-8 -*-
# Copyright (c) 2021, Aakvatech Ltd. and contributors
# For license information, please see license.txt

"""
HR Domain Functions
Contains all HR-related hook functions including payroll, attendance, employee management, etc.
"""

from __future__ import unicode_literals
import frappe
from frappe import _
import frappe
import os
from frappe.utils.background_jobs import enqueue
from frappe.utils.pdf import get_pdf, cleanup
from io import BytesIO
from PyPDF3 import PdfFileReader, PdfFileWriter
from csf_tz import console
from frappe.model.workflow import apply_workflow
from frappe.utils import cint, flt
from frappe.utils import (
    get_time,
    time_diff,
    get_datetime,
    get_weekday,
    today,
    flt
)
from datetime import datetime, timedelta
from typing import Dict, List
from frappe.query_builder import Criterion
from hrms.hr.utils import validate_active_employee
from frappe.utils import cint, get_datetime, now_datetime, add_days
from hrms.hr.doctype.shift_assignment.shift_assignment import (
    get_actual_start_end_datetime_of_shift,
    get_exact_shift,
    get_shift_for_time,
    get_shift_details,
    get_prev_or_next_shift,
)
from hrms.overrides.employee_payment_entry import get_payment_entry_for_employee
import qrcode
import io
import base64


# =============================================================================
# PAYROLL FUNCTIONS
# =============================================================================

def before_insert_payroll_entry(doc, method):
    enable_payroll_approval = frappe.db.get_single_value(
        "CSF TZ Settings", "enable_payroll_approval"
    )
    if enable_payroll_approval:
        doc.has_payroll_approval = 1


def before_insert_salary_slip(doc, method):
    enable_payroll_approval = frappe.db.get_single_value(
        "CSF TZ Settings", "enable_payroll_approval"
    )
    if enable_payroll_approval:
        doc.has_payroll_approval = 1


def before_cancel_payroll_entry(doc, method):
    if not doc.has_payroll_approval:
        return

    doc.ignore_linked_doctypes = "GL Entry"
    salary_slips = frappe.get_all(
        "Salary Slip", filters={"payroll_entry": doc.name}, pluck="name"
    )

    journal_entry = None
    if len(salary_slips) > 0:
        for slip in salary_slips:
            try:
                slip_doc = frappe.get_doc("Salary Slip", slip)
                if not journal_entry:
                    journal_entry = slip_doc.journal_entry

                if slip_doc.docstatus == 1:
                    slip_doc.cancel()
                slip_doc.delete()
            except:
                traceback = frappe.get_traceback()
                title = _(f"Error for Salary Slip: <b>{slip_doc.name}</b>")
                frappe.log_error(traceback, title)
                continue

    if journal_entry:
        try:
            jv_doc = frappe.get_doc("Journal Entry", journal_entry)
            if jv_doc.docstatus == 1:
                jv_doc.cancel()
            jv_doc.delete()
        except:
            traceback = frappe.get_traceback()
            title = _(f"Error for Journal Entry: <b>{jv_doc.name}</b>")
            frappe.log_error(traceback, title)
            return


@frappe.whitelist()
def update_slips(payroll_entry):
    ss_list = frappe.get_all("Salary Slip", filters={"payroll_entry": payroll_entry})
    count = 0
    for salary in ss_list:
        ss_doc = frappe.get_doc("Salary Slip", salary.name)
        if ss_doc.docstatus != 0:
            continue
        ss_doc.earnings = []
        ss_doc.deductions = []
        ss_doc.queue_action("save", timeout=4600)
        count += 1

    frappe.msgprint(_("{0} Salary Slips is updated".format(count)))
    return count


@frappe.whitelist()
def update_slip(salary_slip):
    ss_doc = frappe.get_doc("Salary Slip", salary_slip)
    if ss_doc.docstatus != 0:
        return
    ss_doc.earnings = []
    ss_doc.deductions = []
    ss_doc.save()
    frappe.msgprint(_("Salary Slips is updated"))
    return "True"


@frappe.whitelist()
def print_slips(payroll_entry):
    enqueue(
        method=enqueue_print_slips,
        queue="short",
        timeout=100000,
        is_async=True,
        job_name="print_salary_slips",
        kwargs=payroll_entry,
    )


def enqueue_print_slips(kwargs):
    console("Start Printing")
    payroll_entry = kwargs
    ss_data = frappe.get_all("Salary Slip", filters={"payroll_entry": payroll_entry})
    ss_list = []
    for i in ss_data:
        ss_list.append(i.name)
    doctype = dict({"Salary Slip": ss_list})
    print_format = ""
    default_print_format = frappe.db.get_value(
        "Property Setter",
        dict(property="default_print_format", doc_type="Salary Slip"),
        "value",
    )
    if default_print_format:
        print_format = default_print_format
    else:
        print_format = "Standard"

    pdf = download_multi_pdf(
        doctype, payroll_entry, format=print_format, no_letterhead=0
    )
    if pdf:
        ret = frappe.get_doc(
            {
                "doctype": "File",
                "attached_to_doctype": "Payroll Entry",
                "attached_to_name": payroll_entry,
                "folder": "Home/Attachments",
                "file_name": payroll_entry + ".pdf",
                "file_url": "/files/" + payroll_entry + ".pdf",
                "content": pdf,
            }
        )
        ret.save(ignore_permissions=1)
        console("Printing Finished", "The PDF file is ready in attatchments")
        return ret


def download_multi_pdf(doctype, name, format=None, no_letterhead=0):
    output = PdfFileWriter()
    if isinstance(doctype, dict):
        for doctype_name in doctype:
            for doc_name in doctype[doctype_name]:
                try:
                    console(doc_name)
                    pdf_data = frappe.get_print(
                        doctype_name,
                        doc_name,
                        format,
                        as_pdf=True,
                        output=None,
                        no_letterhead=no_letterhead,
                    )

                    # Convert the PDF bytes into a file-like object
                    pdf_file = BytesIO(pdf_data)

                    # Create a PdfFileReader from the byte stream (file-like object)
                    reader = PdfFileReader(pdf_file)

                    # Add each page from the reader to the writer
                    for page_num in range(reader.getNumPages()):
                        output.addPage(reader.getPage(page_num))

                except Exception:
                    frappe.log_error(
                        f"Permission Error on doc {doc_name} of doctype {doctype_name}"
                    )
        frappe.local.response.filename = f"{name}.pdf"
    return read_multi_pdf(output)


def read_multi_pdf(output):
    fname = os.path.join("/tmp", "frappe-pdf-{0}.pdf".format(frappe.generate_hash()))
    output.write(open(fname, "wb"))

    with open(fname, "rb") as fileobj:
        filedata = fileobj.read()

    return filedata


@frappe.whitelist()
def create_journal_entry(payroll_entry):
    payroll_entry_doc = frappe.get_doc("Payroll Entry", payroll_entry)
    if (
        payroll_entry_doc.docstatus != 1
        or payroll_entry_doc.salary_slips_submitted == 1
    ):
        return
    draft_slips_count = frappe.db.count(
        "Salary Slip", filters={"payroll_entry": payroll_entry, "docstatus": 0}
    )

    if draft_slips_count > 0:
        frappe.throw(_("Salary Slips are not submitted"))
    else:
        submitted_ss = payroll_entry_doc.get_sal_slip_list(ss_status=1, as_dict=True)
        jv_name = payroll_entry_doc.make_accrual_jv_entry(submitted_ss)
        jv_url = frappe.utils.get_url_to_form("Journal Entry", jv_name)
        si_msgprint = _("Journal Entry Created <a href='{0}'>{1}</a>").format(
            jv_url, jv_name
        )
        frappe.msgprint(si_msgprint)
        return "True"


def before_update_after_submit(doc, method):
    if not doc.has_payroll_approval:
        return

    # submit salary slips directly if payroll entry is approved
    if "Approved" in doc.workflow_state:
        doc.submit_salary_slips()
        return

    salary_slips = frappe.get_all(
        "Salary Slip", filters={"payroll_entry": doc.name}, pluck="name"
    )
    if len(salary_slips) == 0:
        return

    params = {"salary_slips": salary_slips, "action": get_workflow_action(doc)}

    enqueue(
        method=enqueue_apply_workflow_for_salary_slips,
        queue="short",
        timeout=100000,
        is_async=True,
        job_name="apply_workflow_for_salary_slips",
        kwargs=params,
    )


def get_workflow_action(doc):
    if doc.workflow_state == "Approval Requested":
        return "Submit"
    elif doc.workflow_state == "Change Requested":
        return "Reject"
    elif "Reviewed" in doc.workflow_state:
        return "Submit"


def enqueue_apply_workflow_for_salary_slips(kwargs):
    for slip in kwargs.get("salary_slips"):
        slip_doc = frappe.get_doc("Salary Slip", slip)

        if kwargs.get("action") == "Reject" and slip_doc.workflow_state == "Open":
            continue
        elif (
            kwargs.get("action") == "Submit"
            and slip_doc.workflow_state == "Ongoing Approval"
        ):
            continue
        elif kwargs.get("action") == "Submit" and slip_doc.workflow_state == "Approved":
            continue
        elif (
            kwargs.get("action") == "Cancel" and slip_doc.workflow_state == "Cancelled"
        ):
            continue
        elif not kwargs.get("action"):
            continue

        try:
            apply_workflow(slip_doc, kwargs.get("action"))

        except:
            traceback = frappe.get_traceback()
            title = _(f"Error for Salary Slip: <b>{slip_doc.name}</b>")
            frappe.log_error(traceback, title)
            continue


@frappe.whitelist()
def generate_component_in_salary_slip_update(doc, method):
    if not doc.name.upper().startswith("NEW") and frappe.db.get_single_value(
        "CSF TZ Settings", "ot_module"
    ):
        base = 0
        list = []

        for component in doc.earnings:
            if str(component.salary_component).upper() == "BASIC":
                base = component.amount / doc.payment_days * doc.total_working_days
                list.append(component)
        if base == 0:
            f"Basic Component not Found on this Salary Slip: <b>{doc.name}</b>"

        for component in doc.salary_slip_ot_component:
            earning_dict = frappe.new_doc("Salary Detail")
            earning_dict.parent = doc.name
            earning_dict.parenttype = doc.doctype
            earning_dict.parentfield = "earnings"
            earning_dict.salary_component = component.salary_component
            earning_dict.amount = calculate_amount(
                base, component.no_of_hours, component.salary_component
            )
            list.append(earning_dict)

            doc.earnings = []
            doc.earnings.extend(list)
            doc.calculate_net_pay()


@frappe.whitelist()
def generate_component_in_salary_slip_insert(doc, method):
    if frappe.db.get_single_value("CSF TZ Settings", "ot_module"):
        doc.salary_slip_ot_component = []
        employee = frappe.get_doc("Employee", doc.employee)
        doc.run_method("get_emp_and_leave_details")
        base = 0
        list = []
        for component in doc.earnings:
            if str(component.salary_component).upper() == "BASIC":
                base = component.amount / doc.payment_days * doc.total_working_days
                list.append(component)
        if base == 0:
            frappe.throw(
                f"Basic Component not Found on this Salary Slip: <b>{doc.name}</b>"
            )

        for component in employee.employee_ot_component:
            component.doctype = "Salary Slip OT Component"
            component.parentfield = "salary_slip_ot_component"
            doc.salary_slip_ot_component.append(component)

            earning_dict = frappe.new_doc("Salary Detail")
            earning_dict.parent = doc.name
            earning_dict.parenttype = doc.doctype
            earning_dict.parentfield = "earnings"
            earning_dict.salary_component = component.salary_component
            earning_dict.amount = calculate_amount(
                base, component.no_of_hours, component.salary_component
            )
            list.append(earning_dict)

            doc.earnings = []
            doc.earnings.extend(list)
            doc.calculate_net_pay()


@frappe.whitelist()
def calculate_amount(base, no_of_hours, salary_component):
    working_hours_per_month = frappe.db.get_single_value(
        "CSF TZ Settings", "working_hours_per_month"
    )
    based_on_hourly_rate, hourly_rate = frappe.db.get_value(
        "Salary Component", salary_component, ["based_on_hourly_rate", "hourly_rate"]
    )
    if based_on_hourly_rate:
        calc = (
            (flt(base) / flt(working_hours_per_month))
            * flt(no_of_hours)
            * (flt(hourly_rate) / 100)
        )
        return calc
    else:
        frappe.throw(
            f"Hourly Rate not set on this Salary Component: <b>{salary_component}</b>, Please set it and try again."
        )


# =============================================================================
# ATTENDANCE FUNCTIONS
# =============================================================================

def process_overtime(doc, method):
    if not frappe.db.get_single_value("CSF TZ Settings", "enable_overtime_calculation"):
        return

    if (
        doc.status != "Present"
        or not doc.overtime_applicable
        or not doc.in_time
        or not doc.out_time
    ):
        doc.eligible_working_hours = "00:00:00"
        doc.eligible_overtime_normal = "00:00:00"
        doc.eligible_overtime_holiday = "00:00:00"
        doc.excess_overtime_normal = "00:00:00"
        doc.excess_overtime_holiday = "00:00:00"

        return

    shift_type = frappe.get_doc("Shift Type", doc.shift)
    if not shift_type.overtime_holiday:
        frappe.throw(f"Please set overtime holiday in shift type {shift_type.name}")

    start_time = shift_type.start_time
    end_time = shift_type.end_time
    late_entry_grace_period = shift_type.late_entry_grace_period
    early_exit_grace_period = shift_type.early_exit_grace_period

    checkin_time = get_time(str(doc.in_time))
    checkout_time = get_time(str(doc.out_time))

    shift_start_time, excess_in_time = calculate_shift_start_time(
        checkin_time, start_time, late_entry_grace_period
    )

    shift_end_time, excess_out_time = calculate_shift_end_time(
        checkout_time, end_time, early_exit_grace_period
    )

    eligible_working_hours = None
    if get_time(str(shift_end_time)) > get_time(str(shift_start_time)):
        eligible_working_hours = time_diff(str(shift_end_time), str(shift_start_time))
    else:
        eligible_working_hours = time_diff(str(shift_start_time), str(shift_end_time))

    threshold = get_weekday_threshold(shift_type, doc.attendance_date)

    is_holiday = get_holiday_status(shift_type.overtime_holiday, doc.attendance_date)

    set_eligible_and_excess_overtime(
        doc,
        excess_in_time,
        excess_out_time,
        eligible_working_hours,
        threshold,
        is_holiday,
    )


def calculate_shift_start_time(checkin_time, start_time, late_entry_grace_period):
    excess_in_time = None
    shift_start_time = None

    if checkin_time <= start_time:
        excess_in_time = time_diff(str(start_time), str(checkin_time))
        shift_start_time = checkin_time
    else:
        _shift_start_time = checkin_time
        if late_entry_grace_period:
            grace_start_time = (
                datetime.combine(datetime.today(), start_time)
                + timedelta(minutes=late_entry_grace_period)
            ).time()
            if checkin_time <= grace_start_time:
                _shift_start_time = start_time

        shift_start_time = _shift_start_time

    return shift_start_time, excess_in_time


def calculate_shift_end_time(checkout_time, end_time, early_exit_grace_period):
    excess_out_time = None
    shift_end_time = None

    if checkout_time >= end_time:
        excess_out_time = time_diff(str(checkout_time), str(end_time))
        shift_end_time = checkout_time
    else:
        _shift_end_time = checkout_time
        if early_exit_grace_period:
            grace_end_time = (
                datetime.combine(datetime.today(), end_time)
                - timedelta(minutes=early_exit_grace_period)
            ).time()
            if checkout_time >= grace_end_time:
                _shift_end_time = end_time

    if _shift_end_time:
        shift_end_time = _shift_end_time
    else:
        shift_end_time = "00:00:00"
    return shift_end_time, excess_out_time


def get_weekday_threshold(shift_type, attendance_date):
    day = get_weekday(get_datetime(attendance_date))
    threshold = None
    if day == "Monday":
        threshold = shift_type.monday_threshold

    if day == "Tuesday":
        threshold = shift_type.tuesday_threshold

    if day == "Wednesday":
        threshold = shift_type.wednesday_threshold

    if day == "Thursday":
        threshold = shift_type.thursday_threshold

    if day == "Friday":
        threshold = shift_type.friday_threshold

    if day == "Saturday":
        threshold = shift_type.saturday_threshold

    if day == "Sunday":
        threshold = shift_type.sunday_threshold

    return threshold


def get_holiday_status(holiday_list, attendance_date):
    return frappe.db.exists("Holiday", {"parent": holiday_list, "holiday_date": attendance_date})


def set_eligible_and_excess_overtime(
    doc, excess_in_time, excess_out_time, eligible_working_hours, threshold, is_holiday
):
    total_excess_time = "00:00:00"
    if excess_in_time and excess_out_time:
        total_excess_time = time_diff(
            str(excess_in_time), "00:00:00"
        ) + time_diff(str(excess_out_time), "00:00:00")
    elif excess_in_time:
        total_excess_time = excess_in_time
    elif excess_out_time:
        total_excess_time = excess_out_time

    doc.eligible_working_hours = eligible_working_hours

    if is_holiday:
        if threshold and total_excess_time > threshold:
            doc.eligible_overtime_holiday = threshold
            doc.excess_overtime_holiday = time_diff(str(total_excess_time), str(threshold))
        else:
            doc.eligible_overtime_holiday = total_excess_time
            doc.excess_overtime_holiday = "00:00:00"
        doc.eligible_overtime_normal = "00:00:00"
        doc.excess_overtime_normal = "00:00:00"
    else:
        if threshold and total_excess_time > threshold:
            doc.eligible_overtime_normal = threshold
            doc.excess_overtime_normal = time_diff(str(total_excess_time), str(threshold))
        else:
            doc.eligible_overtime_normal = total_excess_time
            doc.excess_overtime_normal = "00:00:00"
        doc.eligible_overtime_holiday = "00:00:00"
        doc.excess_overtime_holiday = "00:00:00"


# =============================================================================
# ADDITIONAL SALARY FUNCTIONS
# =============================================================================

@frappe.whitelist()
def create_additional_salary_journal(doc, method):
    if frappe.get_value(
        "Salary Component", doc.salary_component, "create_cash_journal"
    ):
        cash_account = frappe.db.get_single_value(
            "CSF TZ Settings", "default_account_for_additional_component_cash_journal"
        )
        if not cash_account:
            frappe.throw(
                _(
                    "Default Account for Additional Salary Cash Journal not found. Please set it in CSF TZ Settings."
                )
            )
        component_account = frappe.db.get_value(
            "Salary Component Account",
            {"parent": doc.salary_component, "company": doc.company},
            "account",
        )
        if not component_account:
            frappe.throw(
                _(
                    f"Salary Component Account not found for {doc.salary_component} in {doc.company}. Please set it in Salary Component."
                )
            )

        journal_entry = frappe.new_doc("Journal Entry")
        journal_entry.voucher_type = "Cash Entry"
        journal_entry.company = doc.company
        journal_entry.posting_date = doc.payroll_date
        journal_entry.user_remark = (
            doc.doctype + " " + doc.name + " for " + doc.employee_name
        )

        if method == "on_submit":
            journal_entry.append(
                "accounts",
                {
                    "account": component_account,
                    "debit_in_account_currency": doc.amount,
                    "reference_type": doc.doctype,
                    "reference_name": doc.name,
                },
            )
            journal_entry.append(
                "accounts",
                {
                    "account": cash_account,
                    "credit_in_account_currency": doc.amount,
                    "reference_type": doc.doctype,
                    "reference_name": doc.name,
                },
            )
        elif method == "on_cancel":
            journal_entry.append(
                "accounts",
                {
                    "account": component_account,
                    "credit_in_account_currency": doc.amount,
                    "reference_type": doc.doctype,
                    "reference_name": doc.name,
                },
            )
            journal_entry.append(
                "accounts",
                {
                    "account": cash_account,
                    "debit_in_account_currency": doc.amount,
                    "reference_type": doc.doctype,
                    "reference_name": doc.name,
                },
            )

        journal_entry.save(ignore_permissions=True)

        if method == "on_submit":
            # remark this since the field of 'journal_name' is not available in Additional Salary
            # frappe.set_value(doc.doctype, doc.name, "journal_name", journal_entry.name)
            msg_to_print = (
                doc.doctype + " journal " + journal_entry.name + " has been created."
            )
        elif method == "on_cancel":
            msg_to_print = (
                doc.doctype
                + " reverse journal "
                + journal_entry.name
                + " has been created."
            )
        frappe.msgprint(msg_to_print)
    if doc.auto_created_based_on:
        frappe.set_value(
            "Additional Salary",
            doc.auto_created_based_on,
            "last_transaction_amount",
            doc.amount,
        )


@frappe.whitelist()
def generate_additional_salary_records():
    today_date = today()
    auto_repeat_frequency = {"Monthly": 1, "Annually": 12}
    additional_salary_list = frappe.get_all(
        "Additional Salary",
        filters={
            "docstatus": "1",
            "auto_repeat_frequency": ("!=", "None"),
            "auto_repeat_end_date": ("!=", ""),
            "auto_repeat_end_date": (">=", today_date),
        },
        fields={
            "name",
            "auto_repeat_end_date",
            "last_transaction_date",
            "last_transaction_amount",
            "auto_repeat_frequency",
            "payroll_date",
            "employee",
            "employee_name",
            "salary_component",
            "type",
            "overwrite_salary_structure_amount",
            "amount",
            "company",
        },
    )

    for additional_salary in additional_salary_list:
        if additional_salary.last_transaction_date:
            last_transaction_date = additional_salary.last_transaction_date
        else:
            last_transaction_date = additional_salary.payroll_date

        next_transaction_date = frappe.utils.add_months(
            last_transaction_date, auto_repeat_frequency[additional_salary.auto_repeat_frequency]
        )

        if next_transaction_date <= frappe.utils.getdate(today_date):
            new_additional_salary = frappe.get_doc(
                {
                    "doctype": "Additional Salary",
                    "employee": additional_salary.employee,
                    "employee_name": additional_salary.employee_name,
                    "salary_component": additional_salary.salary_component,
                    "type": additional_salary.type,
                    "overwrite_salary_structure_amount": additional_salary.overwrite_salary_structure_amount,
                    "amount": additional_salary.amount,
                    "payroll_date": next_transaction_date,
                    "company": additional_salary.company,
                    "auto_created_based_on": additional_salary.name,
                }
            )
            new_additional_salary.save(ignore_permissions=True)
            new_additional_salary.submit()

            frappe.set_value(
                "Additional Salary",
                additional_salary.name,
                "last_transaction_date",
                next_transaction_date,
            )


@frappe.whitelist()
def get_employee_base_salary_in_hours(employee, payroll_date):
    last_salary_assignment = frappe.db.get_value(
        "Salary Structure Assignment",
        {
            "employee": employee,
            "from_date": ("<=", payroll_date),
            "docstatus": 1,
        },
        ["base"],
        order_by="from_date desc",
        as_dict=True,
    )
    if not last_salary_assignment:
        frappe.throw(
            _(
                "No Salary Structure Assignment found for employee {0} on or before {1}. Please create one and try again."
            ).format(employee, payroll_date)
        )
    working_hours_per_month = frappe.db.get_single_value(
        "CSF TZ Settings", "working_hours_per_month"
    )
    if not working_hours_per_month:
        frappe.throw(
            _(
                "Working Hours per Month not defind in CSF TZ Settings. Define it there and try again."
            )
        )
    base_salary_in_hours = (last_salary_assignment.base or 0) / working_hours_per_month
    return {"base_salary_in_hours": base_salary_in_hours}


def set_employee_base_salary_in_hours(doc, method):
    if doc.based_on_hourly_rate:
        doc.payroll_date = str(doc.payroll_date)
        base_salary_in_hours = get_employee_base_salary_in_hours(
            doc.employee, doc.payroll_date
        )["base_salary_in_hours"]
        doc.amount = doc.hourly_rate / 100 * doc.no_of_hours * base_salary_in_hours


# =============================================================================
# EMPLOYEE CHECKIN FUNCTIONS
# =============================================================================

def validate_employee_checkin(doc, method):
    override_fetch_shift_details = frappe.db.get_single_value(
        "CSF TZ Settings", "override_fetch_shift_details"
    )
    if override_fetch_shift_details == 0:
        return

    validate_active_employee(doc.employee)
    doc.validate_duplicate_log()
    shift_timings = get_employee_shift_timings(
        doc.employee, get_datetime(doc.time), True
    )
    shift_actual_timings = get_exact_shift(shift_timings, get_datetime(doc.time))
    if shift_actual_timings:
        if (
            shift_actual_timings.shift_type.determine_check_in_and_check_out
            == "Strictly based on Log Type in Employee Checkin"
            and not doc.log_type
            and not doc.skip_auto_attendance
        ):
            frappe.throw(
                _(
                    "Log Type is required for check-ins falling in the shift: {0}."
                ).format(shift_actual_timings.shift_type.name)
            )
        if not doc.attendance:
            doc.shift = shift_actual_timings.shift_type.name
            doc.shift_actual_start = shift_actual_timings.actual_start
            doc.shift_actual_end = shift_actual_timings.actual_end
            doc.shift_start = shift_actual_timings.start_datetime
            doc.shift_end = shift_actual_timings.end_datetime
    else:
        doc.shift = None


def get_employee_shift_timings(
    employee: str, for_timestamp: datetime = None, consider_default_shift: bool = False
) -> List[Dict]:
    """Returns previous shift, current/upcoming shift, next_shift for the given timestamp and employee"""
    if for_timestamp is None:
        for_timestamp = now_datetime()

    # write and verify a test case for midnight shift.
    prev_shift = curr_shift = next_shift = None
    curr_shift = get_employee_shift(
        employee, for_timestamp, consider_default_shift, "forward"
    )
    if curr_shift:
        next_shift = get_employee_shift(
            employee,
            curr_shift.start_datetime + timedelta(days=1),
            consider_default_shift,
            "forward",
        )
    prev_shift = get_employee_shift(
        employee,
        (curr_shift.start_datetime if curr_shift else for_timestamp)
        + timedelta(days=-1),
        consider_default_shift,
        "reverse",
    )

    if curr_shift:
        # adjust actual start and end times if they are overlapping with grace period (before start and after end)
        if prev_shift:
            curr_shift.actual_start = (
                prev_shift.end_datetime
                if curr_shift.actual_start < prev_shift.end_datetime
                else curr_shift.actual_start
            )
            prev_shift.actual_end = (
                curr_shift.actual_start
                if prev_shift.actual_end > curr_shift.actual_start
                else prev_shift.actual_end
            )
        if next_shift:
            next_shift.actual_start = (
                curr_shift.end_datetime
                if next_shift.actual_start < curr_shift.end_datetime
                else next_shift.actual_start
            )
            curr_shift.actual_end = (
                next_shift.actual_start
                if curr_shift.actual_end > next_shift.actual_start
                else curr_shift.actual_end
            )

    return prev_shift, curr_shift, next_shift


def get_employee_shift(
    employee: str,
    for_timestamp: datetime = None,
    consider_default_shift: bool = False,
    next_shift_direction: str = None,
) -> Dict:
    shift_details = {}
    shifts_for_date = get_shifts_for_date(employee, for_timestamp)
    if shifts_for_date:
        shift_details = get_shift_for_time(shifts_for_date, for_timestamp)

    # if shift assignment is not found, consider default shift
    default_shift = frappe.db.get_value(
        "Employee", employee, "default_shift", cache=True
    )
    if not shift_details and consider_default_shift:
        shift_details = get_shift_details(default_shift, for_timestamp)

    # if no shift is found, find next or prev shift assignment based on direction
    if not shift_details and next_shift_direction:
        shift_details = get_prev_or_next_shift(
            employee,
            for_timestamp,
            consider_default_shift,
            default_shift,
            next_shift_direction,
        )

    return shift_details or {}


def get_shifts_for_date(employee: str, for_timestamp: datetime) -> List[Dict[str, str]]:
    """Returns list of shifts with details for given date"""
    for_date = for_timestamp.date()

    assignment = frappe.qb.DocType("Shift Assignment")
    shift_assignments = (
        frappe.qb.from_(assignment)
        .select(
            assignment.name,
            assignment.shift_type,
            assignment.start_date,
            assignment.end_date,
        )
        .where(
            (assignment.employee == employee)
            & (assignment.docstatus == 1)
            & (assignment.status == "Active")
            & (assignment.start_date <= for_date)
            & (
                Criterion.any(
                    [
                        assignment.end_date.isnull(),
                        (
                            assignment.end_date.isnotnull()
                            # for midnight shifts, valid assignments are upto 1 day prior
                            & (for_date <= assignment.end_date)
                        ),
                    ]
                )
            )
        )
    ).run(as_dict=True)
    return shift_assignments


# =============================================================================
# EMPLOYEE ADVANCE FUNCTIONS
# =============================================================================

def execute_employee_advance(doc, method):
    if doc.docstatus != 1:
        return

    if not doc.travel_request_ref:
        return

    try:
        payment_entry = create_payment_entry_for_advance(doc)
        doc.reload()

    except Exception as e:
        frappe.throw(f"Error during Employee Advance submission: {str(e)}")


def create_payment_entry_for_advance(doc):
    try:
        payment_entry = get_payment_entry_for_employee("Employee Advance", doc.name)

        if payment_entry:
            payment_entry.reference_no = doc.name
            payment_entry.reference_date = frappe.utils.nowdate()

            # payment_entry.submit()
            frappe.msgprint(f"Payment Entry {payment_entry.name} created successfully")

            return payment_entry

    except Exception as e:
        frappe.throw(f"Error creating Payment Entry: {str(e)}")


# =============================================================================
# EMPLOYEE QR CODE FUNCTIONS
# =============================================================================

@frappe.whitelist()
def generate_contact_qr(employee):
    employee_doc = frappe.get_doc("Employee", employee)

    # Retrieve contact details
    phone = employee_doc.cell_number
    email = employee_doc.personal_email or employee_doc.company_email

    # Check if both phone and email are missing
    if not phone and not email:
        frappe.throw(_("Employee must have at least a phone number or an email to generate a QR Code."))

    # Create vCard format string
    vcard = f"""BEGIN:VCARD
VERSION:3.0
N:{employee_doc.last_name};{employee_doc.first_name};;;
FN:{employee_doc.employee_name}
ORG:{frappe.defaults.get_global_default('company')}
TITLE:{employee_doc.designation}
TEL;TYPE=WORK,VOICE:{phone or ''}
EMAIL;TYPE=WORK:{email or ''}
END:VCARD"""

    # Generate QR code
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
        mask_pattern=7
    )
    qr.add_data(vcard)
    qr.make(fit=True)

    # Create QR code image
    img = qr.make_image(fill_color="black", back_color="white")

    # Convert to base64 for displaying in the frontend
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    qr_base64 = base64.b64encode(buffer.getvalue()).decode()

    return qr_base64
